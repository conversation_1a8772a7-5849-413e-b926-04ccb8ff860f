<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Laravel\Scout\Searchable;

class Sector extends Model
{
    /** @use HasFactory<\Database\Factories\SectorFactory> */
    use HasFactory, Searchable;

    protected $table = 'Sector';

    protected $fillable = [
        'district_id',
        'name',
        'code',
        'capture_year',
        'source',
        'geojson',
        'shape_length',
        'shape_area',
        'population',
        'description',
        'latitude',
        'longitude',
        'geometry',
        'centroid',
        'full_address_en',
        'full_address_fr',
        'full_address_rw',
        'sectorDistrictName',
    ];

    protected $casts = [
        'geojson' => 'array',
    ];

    public function toSearchableArray(): array
    {

        $array = $this->toArray();
        unset($array['geometry'], $array['centroid']);

        return array_merge($array, [
            'id' => (string) $this->id,
            'name' => $this->name,
            'code' => (string) $this->code,
            'latitude' => (string) $this->latitude,
            'longitude' =>  (string) $this->longitude,
        ]);
    }

    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class);
    }

    public function cell(): HasMany
    {
        return $this->hasMany(Cell::class);
    }
}
