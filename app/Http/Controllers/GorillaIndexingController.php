<?php

namespace App\Http\Controllers;

use App\Data\SearchData;
use App\Http\Requests\SearchRequest;
use App\Services\GorillaIndexingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class GorillaIndexingController extends Controller
{
    public function __construct(private GorillaIndexingService $service) {}

    public function searchApi(SearchRequest $request)
    {
        $rateLimitKey = 'search-api:' . $request->ip();

        if (RateLimiter::tooManyAttempts($rateLimitKey, $perMinute = 20)) {
            $seconds = RateLimiter::availableIn($rateLimitKey);
            return response()->json([
                'error' => 'Too many requests. Please try again in ' . $seconds . ' seconds.'
            ], 429);
        }

        RateLimiter::increment($rateLimitKey);

        $data = SearchData::from($request->validated());
        $searchResults = $this->service->search($data, isApiRequest: true);
        return response()->json($searchResults);
    }

    public function search(SearchRequest $request)
    {
        $rateLimitKey = 'search-api:' . $request->ip();

        if (RateLimiter::tooManyAttempts($rateLimitKey, $perMinute = 20)) {
            $seconds = RateLimiter::availableIn($rateLimitKey);
            return response()->json([
                'error' => 'Too many requests. Please try again in ' . $seconds . ' seconds.'
            ], 429);
        }

        RateLimiter::increment($rateLimitKey);

        $data = SearchData::from($request->validated());
        $searchResults = $this->service->search($data, isApiRequest: false);
        return response()->json($searchResults);
    }
}
