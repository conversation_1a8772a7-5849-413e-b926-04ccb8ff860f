<?php

namespace App\Services;

use App\Data\SearchData;
use App\Exceptions\BadRequest;
use App\Models\Cell;
use App\Models\District;
use App\Models\Province;
use App\Models\Sector;
use App\Models\Village;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Http;

class GorillaIndexingService
{

    public function search(SearchData $query, $isApiRequest = false): array
    {
        $checkCache = $this->isSearchResultKeyCached($query, $isApiRequest);
        if ($checkCache !== 'no_cache') {
            $returnData = Redis::get($checkCache);
            if ($returnData !== null) {
                $returnData = json_decode($returnData, true);
                if (is_array($returnData))  return $returnData;
            }
            Log::info('Cache miss or invalid cache data for search query: ' . $checkCache);
        }

        $returnResult = $this->searchIndex($query, $isApiRequest);
        $this->cacheResult($query, $returnResult);

        return $returnResult;
    }

    public function searchIndex(SearchData $query, $isApiRequest = false): array
    {

        if (preg_match('/^-?\d+\.\d+,-?\d+\.\d+$/', $query->searchQuery)) {

            $lat = explode(',', $query->searchQuery)[0];
            $long = explode(',', $query->searchQuery)[1];
            return $this->searchByCoordinates($lat, $long, $isApiRequest);
        }

        if (preg_match('/k[kKnN]|k[gG]|K[Kk]|[kK][nN]/', $query->searchQuery)) {
            return $this->searchInNominatim($query->searchQuery);
        }
        return $this->searchByText($query, $isApiRequest);
    }

    public function searchByText(SearchData $query, $isApiRequest = false): array
    {

        switch ($query->filterData) {
            case 'province':
                return $this->searchInProvince($query, $isApiRequest);
                break;
            case 'district':
                return $this->searchInDistrict($query, $isApiRequest);
                break;
            case 'sector':
                return $this->searchInSector($query, $isApiRequest);
                break;
            case 'cell':
                return $this->searchInCell($query, $isApiRequest);
                break;
            case 'village':
                return $this->searchInVillage($query, $isApiRequest);
                break;
            case 'all':
                return $this->searchInAll($query, $isApiRequest);
                break;
        }
        return $this->emptySearchResult();
    }

    public function searchByCoordinates(string $latitude, string $longitude, $isApiRequest = false): array
    {
        $pointWKT = "POINT($longitude $latitude)";
        $selectFields = $isApiRequest
            ? 'id, name, latitude, longitude, full_address_en, full_address_fr, full_address_rw, villageCellName, villageSectorName, villageDistrictName'
            : 'id, name, latitude, longitude, full_address_en, full_address_fr, full_address_rw, villageCellName, villageSectorName, villageDistrictName, geojson';

        try {
            $village = DB::selectOne(
                "SELECT 
                    $selectFields
                FROM Village
                WHERE ST_Contains(geometry, ST_GeomFromText(?, 4326))
                LIMIT 1",
                [$pointWKT]
            );

            if (!$village) {
                $village = DB::selectOne(
                    "SELECT 
                        $selectFields
                    FROM Village
                    WHERE ST_Distance_Sphere(centroid, ST_GeomFromText(?, 4326)) <= 1000
                    ORDER BY ST_Distance_Sphere(centroid, ST_GeomFromText(?, 4326)) ASC
                    LIMIT 1",
                    [$pointWKT, $pointWKT]
                );
            }

            if (!$village) {
                return [
                    'error' => 'No village found'
                ];
            }

            $result = [
                'key' => md5($village->full_address_en),
                'name' => $village->name,
                'latitude' => $village->latitude,
                'longitude' => $village->longitude,
                'address' => $village->full_address_en,
                'district' => $village->villageDistrictName,
                'sector' => $village->villageSectorName,
                'cell' => $village->villageCellName,
                'village' => $village->name
            ];

            if (!$isApiRequest) {
                $result['geojson'] = $village->geojson;
            }

            return $result;
        } catch (\Throwable $e) {
            Log::error("Error in searchByCoordinates: {$e->getMessage()}");
            throw new BadRequest('An error occurred while processing your request.');
        }
    }

    public function searchInProvince(SearchData $query, $isApiRequest = false): array
    {
        $selectFields = $isApiRequest
            ? ['id', 'name_en', 'name_local', 'latitude', 'longitude']
            : ['id', 'name_en', 'name_local', 'geojson', 'latitude', 'longitude'];

        $results = Province::search($query->searchQuery)
            ->query(function ($query) use ($selectFields) {
                $query->select($selectFields);
            })
            ->get()
            ->map(function ($province) {
                return [
                    'type' => 'province',
                    'name' => $province->name_en,
                    'name_local' => $province->name_local,
                    'latitude' => $province->latitude,
                    'longitude' => $province->longitude,
                    ...($province->geojson ? ['geojson' => $province->geojson] : []),
                    'key' => md5($province->name_en)
                ];
            })
            ->toArray();

        $returndata = $this->emptySearchResult();
        $returndata['provinces'] = $results;
        $returndata['searchQuery'] = $query->searchQuery;
        return $returndata;
    }

    public function searchInDistrict(SearchData $searchData, $isApiRequest = false): array
    {
        $selectFields = $isApiRequest
            ? ['id', 'name', 'latitude', 'longitude', 'full_address_en', 'full_address_fr', 'full_address_rw']
            : ['id', 'name',  'geojson', 'latitude', 'longitude', 'full_address_en', 'full_address_fr', 'full_address_rw'];

        $results = District::search($searchData->searchQuery)
            ->take(2)
            ->query(function ($query) use ($selectFields) {
                $query->select($selectFields);
            })
            ->get()
            ->map(function ($district) use ($searchData) {
                $address = match ($searchData->lang) {
                    'en' => $district->full_address_en,
                    'fr' => $district->full_address_fr,
                    'rw' => $district->full_address_rw,
                    default => $district->full_address_en,
                };
                return [
                    'type' => 'district',
                    'name' => $district->name,
                    'latitude' => $district->latitude,
                    'longitude' => $district->longitude,
                    'address' => $address,
                    ...($district->geojson ? ['geojson' => $district->geojson] : []),
                    'key' => md5($district->name)
                ];
            })
            ->toArray();

        $returndata = $this->emptySearchResult();
        $returndata['districts'] = $results;
        $returndata['searchQuery'] = $searchData->searchQuery;
        return $returndata;
    }

    public function searchInSector(SearchData $searchData, $isApiRequest = false): array
    {
        $selectFields = $isApiRequest
            ? ['id', 'name', 'latitude', 'longitude', 'full_address_en', 'full_address_fr', 'full_address_rw', 'sectorDistrictName']
            : ['id', 'name',  'geojson', 'latitude', 'longitude', 'full_address_en', 'full_address_fr', 'full_address_rw', 'sectorDistrictName'];

        $results = Sector::search($searchData->searchQuery)
            ->take(10)
            ->query(function ($query) use ($selectFields, $searchData) {
                $query->select($selectFields)
                    ->when($searchData->hasDistrict, fn($query) => $query->where('sectorDistrictName', 'like', '%' . $searchData->hasDistrict . '%'));
            })
            ->get()
            ->map(function ($sector) use ($searchData) {

                $address = match ($searchData->lang) {
                    'en' => $sector->full_address_en,
                    'fr' => $sector->full_address_fr,
                    'rw' => $sector->full_address_rw,
                    default => $sector->full_address_en,
                };

                return [
                    'type' => 'sector',
                    'name' => $sector->name,
                    'latitude' => $sector->latitude,
                    'longitude' => $sector->longitude,
                    'address' => $address,
                    ...($sector->geojson ? ['geojson' => $sector->geojson] : []),
                    'key' => md5($sector->full_address_en)
                ];
            })
            ->toArray();

        $returndata = $this->emptySearchResult();
        $returndata['sectors'] = $results;
        $returndata['searchQuery'] = $searchData->searchQuery;
        return $returndata;
    }

    public function searchInCell(SearchData $searchData, $isApiRequest = false): array
    {
        $selectFields = $isApiRequest
            ? ['id', 'name', 'latitude', 'longitude', 'full_address_en', 'full_address_fr', 'full_address_rw', 'cellSectorName', 'cellDistrictName']
            : ['id', 'name',  'latitude', 'longitude', 'geojson', 'full_address_en', 'full_address_fr', 'full_address_rw', 'cellSectorName', 'cellDistrictName'];

        $results = Cell::search($searchData->searchQuery)
            ->take(15)
            ->query(function ($query) use ($selectFields, $searchData) {
                $query->select($selectFields)
                    ->when($searchData->hasSector, fn($query) => $query->where('cellSectorName', 'like', '%' . $searchData->hasSector . '%'))
                    ->when($searchData->hasDistrict, fn($query) => $query->where('cellDistrictName', 'like', '%' . $searchData->hasDistrict . '%'));
            })
            ->get()
            ->map(function ($cell) use ($searchData) {

                $address = match ($searchData->lang) {
                    'en' => $cell->full_address_en,
                    'fr' => $cell->full_address_fr,
                    'rw' => $cell->full_address_rw,
                    default => $cell->full_address_en,
                };
                return [
                    'type' => 'cell',
                    'name' => $cell->name,
                    'latitude' => $cell->latitude,
                    'longitude' => $cell->longitude,
                    'address' => $address,
                    ...($cell->geojson ? ['geojson' => $cell->geojson] : []),
                    'key' => md5($cell->full_address_en)
                ];
            })
            ->toArray();

        $returndata = $this->emptySearchResult();
        $returndata['cells'] = $results;
        $returndata['searchQuery'] = $searchData->searchQuery;
        return $returndata;
    }

    public function searchInVillage(SearchData $searchData, $isApiRequest = false): array
    {
        $selectFields = $isApiRequest
            ? ['id', 'name', 'latitude', 'longitude', 'full_address_en', 'full_address_fr', 'full_address_rw', 'villageCellName', 'villageSectorName', 'villageDistrictName']
            : ['id', 'name',  'geojson', 'latitude', 'longitude', 'full_address_en', 'full_address_fr', 'full_address_rw', 'villageCellName', 'villageSectorName', 'villageDistrictName'];

        $results = Village::search($searchData->searchQuery)
            ->take(20)
            ->query(function ($query) use ($selectFields, $searchData) {
                $query->select($selectFields)
                    ->when($searchData->hasCell, fn($query) => $query->where('villageCellName', 'like', '%' . $searchData->hasCell . '%'))
                    ->when($searchData->hasSector, fn($query) => $query->where('villageSectorName', 'like', '%' . $searchData->hasSector . '%'))
                    ->when($searchData->hasDistrict, fn($query) => $query->where('villageDistrictName', 'like', '%' . $searchData->hasDistrict . '%'));
            })
            ->get()
            ->map(function ($village) use ($searchData) {

                $address =  match ($searchData->lang) {
                    'en' => $village->full_address_en,
                    'fr' => $village->full_address_fr,
                    'rw' => $village->full_address_rw,
                    default => $village->full_address_en,
                };

                return [
                    'type' => 'village',
                    'name' => $village->name,
                    'latitude' => $village->latitude,
                    'longitude' => $village->longitude,
                    'address' => $address,
                    ...($village->geojson ? ['geojson' => $village->geojson] : []),
                    'key' => md5($village->full_address_en)
                ];
            })
            ->toArray();

        $returndata = $this->emptySearchResult();
        $returndata['villages'] = $results;
        $returndata['searchQuery'] = $searchData->searchQuery;
        return $returndata;
    }

    public function searchInNominatim(string $searchData): array
    {

        $searchQuery = $searchData;
        $port = env('FORWARD_NOMINATIM_PORTx', 8080);
        $result = Http::get("http://nominatim:$port/search?q=$searchQuery&format=json")->json();
        $formattedResults = array_map(function ($item) {
            $mapResult = $this->searchByCoordinates($item['lat'], $item['lon']);

            return [
                'latitude' => $item['lat'],
                'longitude' => $item['lon'],
                'address' => $mapResult['address'] ?? $item['display_name'] ?? '',
                'name' => $item['name'] ?? '',
                'type' => $item['type'] ?? '',
                'addresstype' => $item['addresstype'] ?? '',
                'moreDetails' => $item['display_name'] ?? '',
                'key' => md5($item['display_name'])
            ];
        }, $result);

        $returndata = $this->emptySearchResult();
        $returndata['others'] = $formattedResults;
        return $returndata;
    }

    public function searchInAll(SearchData $query, $isApiRequest = false): array
    {
        $patternData = $this->parseQuery($query->searchQuery, config('search-pattern'));

        // Handle single level queries
        if (count($patternData) === 1 && array_key_exists('village', $patternData)) {
            return $this->searchInVillage(SearchData::from([
                'searchQuery' => $patternData['village'],
                'lang' => $query->lang,
                'filterData' => 'village'
            ]), $isApiRequest);
        }

        if (count($patternData) === 1 && array_key_exists('cell', $patternData)) {
            return $this->searchInCell(SearchData::from([
                'searchQuery' => $patternData['cell'],
                'lang' => $query->lang,
                'filterData' => 'cell'
            ]), $isApiRequest);
        }

        if (count($patternData) === 1 && array_key_exists('sector', $patternData)) {
            return $this->searchInSector(SearchData::from([
                'searchQuery' => $patternData['sector'],
                'lang' => $query->lang,
                'filterData' => 'sector'
            ]), $isApiRequest);
        }

        if (count($patternData) === 1 && array_key_exists('district', $patternData)) {
            return $this->searchInDistrict(SearchData::from([
                'searchQuery' => $patternData['district'],
                'lang' => $query->lang,
                'filterData' => 'district'
            ]), $isApiRequest);
        }

        if (count($patternData) === 1 && array_key_exists('province', $patternData)) {
            return $this->searchInProvince(SearchData::from([
                'searchQuery' => $patternData['province'],
                'lang' => $query->lang,
                'filterData' => 'province'
            ]), $isApiRequest);
        }

        if (count($patternData) > 1) {
            $levelOrder = ['village', 'cell', 'sector', 'district', 'province'];
            $mainLevel = null;
            $mainQuery = null;
            $extraData = [
                'hasVillage' => null,
                'hasCell' => null,
                'hasSector' => null,
                'hasDistrict' => null,
                'hasProvince' => null,
            ];

            foreach ($levelOrder as $level) {
                if (array_key_exists($level, $patternData)) {
                    if (!$mainLevel) {
                        $mainLevel = $level;
                        $mainQuery = $patternData[$level];
                    } else {
                        // It's an extra filter
                        $extraKey = 'has' . ucfirst($level); // e.g., hasSector
                        $extraData[$extraKey] = $patternData[$level];
                    }
                }
            }

            if (!$mainLevel || !$mainQuery) {
                return $this->emptySearchResult(); // If no valid level found
            }

            // Build the new SearchData object dynamically
            $newSearchData = SearchData::from([
                'searchQuery' => $mainQuery,
                'lang' => $query->lang,
                'filterData' => $mainLevel,
                ...$extraData, // Spread the extra filters
            ]);

            // Route to appropriate search method
            switch ($mainLevel) {
                case 'village':
                    return $this->searchInVillage($newSearchData, $isApiRequest);
                case 'cell':
                    return $this->searchInCell($newSearchData, $isApiRequest);
                case 'sector':
                    return $this->searchInSector($newSearchData, $isApiRequest);
                case 'district':
                    return $this->searchInDistrict($newSearchData, $isApiRequest);
                case 'province':
                    return $this->searchInProvince($newSearchData, $isApiRequest);
            }
        }

        // If we got here, nothing matched
        return $this->emptySearchResult();
    }


    private function emptySearchResult(): array
    {
        return [
            'searchQuery' => null,
            'provinces' => [],
            'districts' => [],
            'sectors' => [],
            'cells' => [],
            'villages' => [],
            'others' => [],
        ];
    }

    private function isSearchResultKeyCached(SearchData $query, $isApiRequest): string
    {

        $isApiRequest = $isApiRequest ? 'yes' : 'no';
        $key = 'search_result_' . md5($query->searchQuery . $query->lang . $query->filterData . $isApiRequest);
        if (Redis::exists($key))  return $key;
        return 'no_cache';
    }

    private function cacheResult(SearchData $query, $cacheData): void
    {
        $key = 'search_result_' . md5($query->searchQuery . $query->lang . $query->filterData);
        try {
            Redis::set($key, json_encode($cacheData), 'EX', 3600);
        } catch (\Exception $e) {
            Log::error('Failed to cache search result for key: ' . $key . '. Error: ' . $e->getMessage());
        }
    }

    public function matchPrefix(string $part, string $prefix): bool|string
    {
        $partWords = preg_split('/\s+/', trim($part)); // Split on whitespace
        $prefixWords = preg_split('/\s+/', trim($prefix));
        $numPrefixWords = count($prefixWords);

        if (count($partWords) < $numPrefixWords) {
            return false;
        }

        $isMulti = $numPrefixWords > 1;
        $match = true;

        for ($i = 0; $i < $numPrefixWords; $i++) {
            $lowerPartWord = strtolower($partWords[$i]);
            $lowerPrefixWord = strtolower($prefixWords[$i]);

            if ($isMulti) {
                similar_text($lowerPartWord, $lowerPrefixWord, $percent);
                if ($percent < 70) {
                    $match = false;
                    break;
                }
            } else {
                if ($lowerPartWord !== $lowerPrefixWord) {
                    $match = false;
                    break;
                }
            }
        }

        if ($match) {
            $nameParts = array_slice($partWords, $numPrefixWords);
            return implode(' ', $nameParts);
        }

        return false;
    }

    public  function hasLanguagePattern(string $query, array $patterns): bool
    {
        $query = trim($query);
        $parts = array_map('trim', explode(',', $query));
        foreach ($parts as $part) {
            if (empty($part)) continue;
            foreach ($patterns as $lang => $levels) {
                foreach ($levels as $level => $prefixes) {
                    foreach ($prefixes as $prefix) {
                        if ($this->matchPrefix($part, $prefix) !== false) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public  function parseQuery(string $query, array $patterns): array
    {
        $query = trim($query);
        $parts = array_map('trim', explode(',', $query));
        $hasPattern = $this->hasLanguagePattern($query, $patterns);

        $levelOrder = ['village', 'cell', 'sector', 'district', 'province'];
        $result = [];

        if (!$hasPattern) {
            // No pattern mode: positional mapping
            if (count($parts) === 1) {
                // Single part without pattern defaults to village
                if (!empty($parts[0])) {
                    $result['village'] = $parts[0];
                }
            } else {
                // Positional mapping for multiple parts
                for ($i = 0; $i < count($parts); $i++) {
                    if ($i >= count($levelOrder)) {
                        break;
                    }
                    if (!empty($parts[$i])) {
                        $result[$levelOrder[$i]] = $parts[$i];
                    }
                }
            }
        } else {
            // Pattern mode
            foreach ($parts as $part) {
                if (empty($part)) continue;
                $bestLevel = '';
                $bestName = '';
                $bestPrefixLength = 0;
                foreach ($patterns as $lang => $levels) {
                    foreach ($levels as $level => $prefixes) {
                        foreach ($prefixes as $prefix) {
                            $name = $this->matchPrefix($part, $prefix);
                            if ($name !== false) {
                                $prefixLength = strlen($prefix);
                                if ($prefixLength > $bestPrefixLength) {
                                    $bestPrefixLength = $prefixLength;
                                    $bestLevel = $level;
                                    $bestName = trim($name);
                                }
                            }
                        }
                    }
                }
                if ($bestLevel && !empty($bestName)) {
                    $result[$bestLevel] = $bestName;
                } else {
                    // If no pattern matches, default to 'cell' unless single-word prefix
                    foreach ($patterns as $lang => $levels) {
                        foreach ($levels as $level => $prefixes) {
                            foreach ($prefixes as $prefix) {
                                if (strtolower($part) === strtolower($prefix)) {
                                    // Single-word prefix (e.g., 'Umudugudu') maps to level
                                    $result[$level] = '';
                                    break 3;
                                }
                            }
                        }
                    }
                    $result['cell'] = $part;
                }
            }
        }

        return $result;
    }
}


/*

we are going to add multiple set  to yes or no in parseQuery , this will help to search when a user type a top revel administration , 

like when a suer searched 'akarere ka kicukiro, kagarama' here can return 
      village = kagarama
      cell = kagarama
      sector = kagarama
      district = kicukiro 
      multiple = yes 

      here we will return 
      village that can 


this will help to search in many tables 


we also have to add patter for kk , kn KG with nominatin
also we will have to add place and location from niminatim 
*/