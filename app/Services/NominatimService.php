<?php

namespace App\Services;

use App\Data\SearchLatLongData;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class NominatimService
{
    public function __construct(protected MapService $mapService) {}

    public function search(string $searchQuery): array
    {
        $port = env('FORWARD_NOMINATIM_PORTx', 8080);

        $result = Http::get("http://nominatim:$port/search?q=$searchQuery&format=json")->json();
        Log::info('Nominatim result: ' . json_encode($result));
        $formattedResults = array_map(function ($item) {
          $data = SearchLatLongData::from([
            'latitude' => $item['lat'],
            'longitude' => $item['lon']
          ]);
            $mapResult = $this->mapService->searchLatitudeLongitude($data);
            $address = $mapResult[0]['address'] ?? $item['display_name'] ?? '';

            return [
                'latitude' => $item['lat'],
                'longitude' => $item['lon'],
                'address' => $address,
                'name' => $item['name'] ?? '',
                'type' => $item['type'] ?? '',
                'addresstype' => $item['addresstype'] ?? '',
                'moreDetails' => $item['display_name'] ?? '',
            ];
        }, $result);

        return $formattedResults;
    }

    public function reverseSearch(string $latitude, string $longitude): string
    {
        $lat = $latitude;
        $long = $longitude;
        $data = SearchLatLongData::from($lat, $long);
        $result = $this->mapService->searchLatitudeLongitude($data);
        return $result[0]['address'] ?? '';
    }
}

/*

we are going to add address[lang]  to every mode l, and add fields for theri refernce , villageCellName, VillageSectorName, VillageDistrictName, VillageProvinceName

now in search we will use nestec array intelige 
        [text1,text2] search in district , sector 
        [text1,text2,text3] search in sector , cell 
        [text1,text2,text3,text4] search in cell , village 


 after this  e will finish      GeneralPlace 

*/