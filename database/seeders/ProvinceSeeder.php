<?php

namespace Database\Seeders;

use App\Models\Province;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Spinen\Geometry\Geometry;
use Throwable;

class ProvinceSeeder extends Seeder
{
    protected $geometry;

    public function __construct(Geometry $geometry)
    {
        $this->geometry = $geometry;
    }

    public function run()
    {

        $geojsonPath = storage_path('dataset/Province_Boundaries.geojson');
        if (!File::exists($geojsonPath)) {
            Log::error('GeoJSON file not found at: ' . $geojsonPath);
            throw new \Exception('GeoJSON file not found at: ' . $geojsonPath);
        }

        $geojsonContent = File::get($geojsonPath);
        $geojson = json_decode($geojsonContent, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('Invalid GeoJSON format: ' . json_last_error_msg());
            throw new \Exception('Invalid GeoJSON format: ' . json_last_error_msg());
        }

        if (!isset($geojson['features']) || empty($geojson['features'])) {
            Log::error('No features found in GeoJSON file');
            throw new \Exception('No features found in GeoJSON file');
        }

        $successCount = 0;
        $errorCount = 0;
        $totalFeatures = count($geojson['features']);

        DB::beginTransaction();
        try {
            foreach ($geojson['features'] as $index => $feature) {
                try {
                    if (!isset($feature['geometry']) || !in_array($feature['geometry']['type'], ['Polygon', 'MultiPolygon'])) {
                        Log::warning("Skipping feature #$index: Invalid or missing geometry type. Properties: " . json_encode($feature['properties'] ?? []));
                        $errorCount++;
                        continue;
                    }

                    $geometryObject = $this->geometry->parseGeoJson(json_encode($feature['geometry']));
                    $geometryWkt = $geometryObject->toWkt();
                    if (empty($geometryWkt)) {
                        Log::warning("Skipping feature #$index: Failed to convert geometry to WKT. Properties: " . json_encode($feature['properties'] ?? []));
                        $errorCount++;
                        continue;
                    }

                    $centroidObject = $geometryObject->centroid();
                    if (!$centroidObject || !method_exists($centroidObject, 'x') || !method_exists($centroidObject, 'y')) {
                        Log::warning("Skipping feature #$index: Failed to calculate valid centroid. Properties: " . json_encode($feature['properties'] ?? []));
                        $errorCount++;
                        continue;
                    }

                    $longitude = $centroidObject->x();
                    $latitude = $centroidObject->y();
                    $centroidWkt = "POINT($longitude $latitude)";
                    if (empty($latitude) || empty($longitude)) {
                        Log::warning("Skipping feature #$index: Invalid centroid coordinates. Properties: " . json_encode($feature['properties'] ?? []));
                        $errorCount++;
                        continue;
                    }

                    $isValidCentroid = DB::selectOne('SELECT ST_IsValid(ST_GeomFromText(?, 4326)) as valid', [$centroidWkt]);
                    if (!$isValidCentroid->valid) {
                        Log::warning("Skipping feature #$index: Invalid centroid WKT syntax. WKT: $centroidWkt. Properties: " . json_encode($feature['properties'] ?? []));
                        $errorCount++;
                        continue;
                    }

                    $properties = $feature['properties'] ?? [];
                    $nameEn = $properties['prov_enlgi'] ?? $properties['name_en'] ?? null;
                    if (empty($nameEn)) {
                        Log::warning("Skipping feature #$index: Missing name_en or prov_enlgi. Properties: " . json_encode($properties));
                        $errorCount++;
                        continue;
                    }

                    if ($nameEn == 'Kigali City') {
                        $properties['name_local'] = 'Umujyi wa Kigali';
                        $properties['name_fr'] = 'Ville de Kigali';
                    }
                    if ($nameEn == 'East') {

                        $properties['name_fr'] = 'Est';
                    }
                    if ($nameEn == 'West') {
                        $properties['name_fr'] = 'Ouest';
                    }
                    if ($nameEn == 'North') {
                        $properties['name_fr'] = 'Nord';
                    }
                    if ($nameEn == 'South') {
                        $properties['name_fr'] = 'Sud';
                    }

                    $data = [
                        'name_en' => $nameEn,
                        'name_local' => $properties['province'] ?? $properties['name_local'] ?? null,
                        'name_fr' => $properties['prov_fran'] ?? $properties['name_fr'] ?? null,
                        'code' => $properties['pr_code'] ?? $properties['code'] ?? null,
                        'capture_year' => $properties['capture'] ?? null,
                        'source' => $properties['source'] ?? null,
                        'geojson' => json_encode($feature['geometry']),
                        'shape_length' => $properties['shape_Length'] ?? null,
                        'shape_area' => $properties['shape_Area'] ?? $properties['area'] ?? null,
                        'population' => $properties['population'] ?? null,
                        'description' => $properties['description'] ?? null,
                        'latitude' => $latitude,
                        'longitude' => $longitude,
                        'geometry' => DB::raw("ST_GeomFromText('{$geometryWkt}', 4326)"),
                        'centroid' => DB::raw("ST_GeomFromText('{$centroidWkt}', 4326)"),
                    ];

                    Province::create($data);
                    $successCount++;
                } catch (Throwable $e) {
                    Log::error("Error processing feature #$index: " . $e->getMessage() . ". Properties: " . json_encode($feature['properties'] ?? []));
                    $errorCount++;
                    continue;
                }
            }

            DB::commit();
            Log::info("Province seeding completed. Total features: $totalFeatures, Successfully created: $successCount, Errors: $errorCount");
        } catch (Throwable $e) {
            DB::rollBack();
            Log::error('Seeding failed: ' . $e->getMessage());
            throw new \Exception('Seeding failed: ' . $e->getMessage());
        }
    }
}
