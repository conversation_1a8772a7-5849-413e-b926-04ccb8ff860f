<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use App\Models\Cell;
use App\Models\District;
use App\Models\Province;
use App\Models\Sector;
use Illuminate\Support\Facades\Log;
use Spinen\Geometry\Geometry;
use Illuminate\Support\Facades\DB;

class CellSeeder extends Seeder
{
    protected $geometry;

    /**
     * Constructor to inject Geometry service
     *
     * @param Geometry $geometry
     */
    public function __construct(Geometry $geometry)
    {
        $this->geometry = $geometry;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $geojsonPath = storage_path('dataset/Cell_level_boundary.geojson');
        if (!File::exists($geojsonPath)) {
            throw new \Exception('GeoJSON file not found at: ' . $geojsonPath);
        }

        $geojson = json_decode(File::get($geojsonPath), true);

        if (!isset($geojson['features']) || empty($geojson['features'])) {
            throw new \Exception('No features found in GeoJSON file');
        }

        // Prepare data for upsert in chunks
        $batchSize = 500;
        $cells = [];

        foreach ($geojson['features'] as $index => $feature) {

            try {
                // Validate geometry
                if (!isset($feature['geometry']) || !in_array($feature['geometry']['type'], ['Polygon', 'MultiPolygon'])) {
                    Log::warning('Invalid or missing geometry for cell: ' . ($feature['properties']['NAME_4'] ?? 'Unknown'));
                    continue;
                }

                // Calculate centroid
                $geometryObject = $this->geometry->parseGeoJson(json_encode($feature['geometry']));
                $geometryWkt = $geometryObject->toWkt();
                $centroidObject = $geometryObject->centroid();
                $longitude = $centroidObject->x();
                $latitude = $centroidObject->y();
                $centroidWkt = "POINT($longitude $latitude)";

                $sector = Sector::where('name', $feature['properties']['NAME_3'])->whereHas('district', function ($query) use ($feature) {
                    $query->where('name', $feature['properties']['NAME_2']);
                })->first();
                $full_address_en =  $sector->district->province->name_en . ', ' .  $feature['properties']['NAME_2'] . ', ' . $feature['properties']['NAME_3'] . ', ' . $feature['properties']['NAME_4'] . ' Cell';
                $full_address_fr =  $sector->district->province->name_fr . ', ' . $feature['properties']['NAME_2'] . ', ' . $feature['properties']['NAME_3'] . ', Cellule de ' . $feature['properties']['NAME_4'];
                $full_address_rw =  $sector->district->province->name_local . ', ' . $feature['properties']['NAME_2'] . ', ' . $feature['properties']['NAME_3'] . ', Akagari ka ' . $feature['properties']['NAME_4'];

                $cells[] = [
                    'name' => $feature['properties']['NAME_4'] ?? null,
                    'code' => $feature['properties']['ID_4	'] ?? null,
                    'geojson' => json_encode($feature['geometry']),
                    'sector_id' => Sector::where('name', $feature['properties']['NAME_3'])->whereHas('district', function ($query) use ($feature) {
                        $query->where('name', $feature['properties']['NAME_2']);
                    })->first()?->id ?? null,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'geometry' => DB::raw("ST_GeomFromText('{$geometryWkt}', 4326)"),
                    'centroid' => DB::raw("ST_GeomFromText('{$centroidWkt}', 4326)"),
                    'full_address_en' => $full_address_en,
                    'full_address_fr' => $full_address_fr,
                    'full_address_rw' => $full_address_rw,
                    'cellSectorName' => $feature['properties']['NAME_3'],
                    'cellDistrictName' => $feature['properties']['NAME_2'],
                ];
            } catch (\Exception $e) {
                Log::error('Error processing cell: ' . ($feature['properties']['cellule_1'] ?? 'Unknown') . ' - ' . $e->getMessage());
                continue;
            }

            // Perform upsert when batch size is reached or at the end
            if (count($cells) >= $batchSize || $index === count($geojson['features']) - 1) {
                Cell::upsert(
                    $cells,
                    ['code'], // Unique key for upsert (assumes 'code' is unique)
                    [
                        'name',
                        'geojson',
                        'sector_id',
                        'latitude',
                        'longitude',
                        'updated_at',
                    ]
                );
                $cells = []; // Clear the batch
            }
        }


        Log::info('Cell seeding completed. ' . Cell::count() . ' cells created on ' . now()->toDateTimeString() . ' CAT.');
    }
}
