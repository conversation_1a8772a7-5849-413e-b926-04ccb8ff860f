<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('GeneralPlace', function (Blueprint $table) {
            $table->id();
            $table->foreignId('village_id')->references('id')->on('Village')->onDelete('restrict');
            $table->string('name')->index();
            $table->string('type')->nullable();
            $table->string('code')->nullable();
            $table->string('layer')->nullable();
            $table->string('latitude')->nullable();
            $table->string('longitude')->nullable();
            $table->integer('capture_year')->nullable();
            $table->string('source')->nullable();
            $table->json('geojson')->nullable();
            $table->integer('population')->nullable();
            $table->text('description')->nullable();
            $table->string('full_address_en')->nullable();
            $table->string('full_address_fr')->nullable();
            $table->string('full_address_rw')->nullable();

            $table->string('placeVillageName')->nullable();
            $table->string('placeCellName')->nullable();
            $table->string('placeSectorName')->nullable();
            $table->string('placeDistrictName')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('GeneralPlace');
    }
    
};
