<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('Sector', function (Blueprint $table) {
            $table->id();
            $table->foreignId('district_id')->references('id')->on('District')->onDelete('restrict');
            $table->string('name')->index();
            $table->string('code')->nullable();
            $table->integer('capture_year')->nullable();
            $table->string('source')->nullable();
            $table->json('geojson');
            $table->float('shape_length')->nullable();
            $table->float('shape_area')->nullable();
            $table->integer('population')->nullable();
            $table->text('description')->nullable();
            $table->decimal('latitude', 15, 7)->nullable();
            $table->decimal('longitude', 15, 7)->nullable();

            $table->string('full_address_en')->nullable();
            $table->string('full_address_fr')->nullable();
            $table->string('full_address_rw')->nullable();

            $table->string('sectorDistrictName')->nullable();
            $table->timestamps();
        });

        DB::statement('ALTER TABLE Sector ADD geometry GEOMETRY NOT NULL SRID 4326');
        DB::statement('ALTER TABLE Sector ADD centroid POINT NOT NULL SRID 4326');
        DB::statement('CREATE SPATIAL INDEX geometry_index ON Sector (geometry)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('Sector');
    }
};
