<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Globe, Building, Home, Hospital, Settings, MapPin, LocateFixed, Menu, X, BrainCircuit, Layers } from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';
import AppLayout from '@/Layouts/AppLayout';

const props = defineProps({
    provinces: { type: Array, default: () => [] },
});

// --- STATE ---
const mapContainer = ref(null);
const map = ref(null);
const searchMode = ref(localStorage.getItem('searchMode') || 'text');
const searchQuery = ref('');
const coordinateForm = ref({ latitude: '', longitude: '' });
const selectedLanguage = ref(localStorage.getItem('mapLanguage') || 'en');
const selectedFilter = ref(localStorage.getItem('mapFilter') || 'all');
const panelsVisible = ref(window.innerWidth >= 640);
const selectedTheme = ref(localStorage.getItem('mapTheme') || 'Default');
const isMapLoaded = ref(false);

const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'others', 'healthFacs'];
const searchResults = ref(Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []])));

const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const searchLayerIds = ref([]);
const searchSourceIds = ref([]);
const selectedResultId = ref(null);
const selectedResultType = ref(null);
const activeGeoJson = ref({ key: null, type: null });

// --- PRIVATE VARIABLES ---
let hoverPopup = null;
let selectedPopup = null;
let hoveredFeature = { id: null, sourceId: null, type: null };
let clickMarker = null;
let currentGeoJsonLayers = [];

// Rwanda bounds (approximate)
const RWANDA_BOUNDS = [
    [28.8617546, -2.8389804], // SW
    [30.8990738, -1.0474083]  // NE
];

// --- CONFIG ---
const MAP_CONFIG = {
    center: [29.8739, -1.9403],
    zoom: 8.5,
    minSearchChars: 3,
    debounceMs: 300,
    fitBoundsPadding: { top: 50, bottom: 50, left: 450, right: 50 },
    maxZoomForFit: 14,
};

const UI_CONFIG = {
    languages: [
        { code: 'rw', name: 'Kinyarwanda' },
        { code: 'en', name: 'English' },
        { code: 'fr', name: 'Français' },
    ],
    filters: [
        { code: 'all', name: 'All', icon: Globe },
        { code: 'province', name: 'Provinces', icon: Building },
        { code: 'district', name: 'Districts', icon: Building },
        { code: 'sector', name: 'Sectors', icon: Home },
        { code: 'cell', name: 'Cells', icon: Home },
        { code: 'village', name: 'Villages', icon: Home },
        { code: 'health_fac', name: 'Health', icon: Hospital },
        { code: 'pattern', name: 'Pattern', icon: BrainCircuit },
    ],
    layerStyles: {
        province: { name: 'Provinces', color: '#1A773E', fillColor: '#1A773E', borderColor: '#1C5172', fillOpacity: 0.15, borderWidth: 1.5 },
        district: { name: 'Districts', color: '#1C5172', fillColor: '#1C5172', borderColor: '#1A773E', fillOpacity: 0.2, borderWidth: 1.5 },
        sector: { name: 'Sectors', color: '#303017', fillColor: '#303017', borderColor: '#1A773E', fillOpacity: 0.2, borderWidth: 1 },
        cell: { name: 'Cells', color: '#1A773E', fillColor: '#1A773E', borderColor: '#1C5172', fillOpacity: 0.2, borderWidth: 0.8 },
        village: { name: 'Villages', color: '#1C5172', fillColor: '#1C5172', borderColor: '#303017', fillOpacity: 0.2, borderWidth: 0.5 },
        healthFac: { name: 'Health Facilities', color: '#d946ef', fillColor: '#d946ef', borderColor: '#c026d3' },
        other: { name: 'Others', color: '#8b5cf6', fillColor: '#8b5cf6', borderColor: '#7c3aed' },
        searchResult: { name: 'Search Result', color: '#1A773E', fillColor: '#1A773E', borderColor: '#1C5172', fillOpacity: 0.2, borderWidth: 2 },
        search_center: { name: 'Search Center', color: '#303017', fillColor: '#303017', borderColor: '#1A773E' },
    },
    highlightStyle: { color: '#1A773E', width: 3, opacity: 0.9 },
    highlightSourceId: 'highlight-source',
    highlightLayerId: 'highlight-layer',
    mapThemes: {
        'Default': {
            url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
            attribution: '© OpenStreetMap © CARTO',
            paint: {}
        },
        'Satellite': {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles © Esri',
            paint: {}
        }
    },
};

// --- COMPUTED ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const canSearch = computed(() => {
    if (searchMode.value === 'coordinates') {
        const lat = parseFloat(coordinateForm.value.latitude);
        const lon = parseFloat(coordinateForm.value.longitude);
        return !isNaN(lat) && !isNaN(lon) && lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180;
    }
    return searchQuery.value.trim().length >= MAP_CONFIG.minSearchChars;
});

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu...',
    en: 'Search for a location...',
    fr: 'Rechercher un lieu...',
}[selectedLanguage.value] || 'Search locations...'));

// --- SMOOTH GEOJSON SWITCHING ---
const smoothSwitchGeoJson = (result) => {
    if (!map.value || !isMapLoaded.value) return;

    // Fade out and remove old layers
    currentGeoJsonLayers.forEach(({ fillId, borderId, sourceId }) => {
        if (map.value.getLayer(fillId)) {
            map.value.setPaintProperty(fillId, 'fill-opacity', 0);
        }
        if (map.value.getLayer(borderId)) {
            map.value.setPaintProperty(borderId, 'line-opacity', 0);
        }
        
        setTimeout(() => {
            if (map.value.getLayer(fillId)) map.value.removeLayer(fillId);
            if (map.value.getLayer(borderId)) map.value.removeLayer(borderId);
            if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);
        }, 300);
    });

    currentGeoJsonLayers = [];

    // If same result clicked, just clear
    if (!result || activeGeoJson.value.key === result.key) {
        activeGeoJson.value = { key: null, type: null };
        return;
    }

    // Add new layer with fade in after a delay
    setTimeout(() => {
        const feature = { 
            type: "Feature", 
            geometry: result.geojson, 
            properties: { ...result, id: result.key, type: result.type } 
        };
        
        const layerIds = addGeoJsonLayer(result.type, result.key, feature, false);
        currentGeoJsonLayers.push(layerIds);
        activeGeoJson.value = { key: result.key, type: result.type };

        // Fade in animation
        setTimeout(() => {
            const layerStyle = UI_CONFIG.layerStyles[result.type] || UI_CONFIG.layerStyles.searchResult;
            if (map.value.getLayer(layerIds.fillId)) {
                map.value.setPaintProperty(layerIds.fillId, 'fill-opacity', layerStyle.fillOpacity);
            }
            if (map.value.getLayer(layerIds.borderId)) {
                map.value.setPaintProperty(layerIds.borderId, 'line-opacity', 1);
            }
        }, 50);

        // Zoom to bounds
        const bounds = new maplibregl.LngLatBounds();
        if (feature.geometry?.bbox) {
            bounds.extend(feature.geometry.bbox);
        } else if (feature.geometry?.coordinates) {
            const extendCoords = (coords) => {
                if (Array.isArray(coords[0])) coords.forEach(extendCoords);
                else bounds.extend(coords);
            };
            extendCoords(feature.geometry.coordinates);
        }
        
        if (!bounds.isEmpty()) {
            map.value.fitBounds(bounds, { padding: 50, duration: 800 });
        }
    }, 300);
};

const toggleGeoJson = (result) => smoothSwitchGeoJson(result);

// --- SEARCH LOGIC ---
const performSearch = debounce(async () => {
    if (!canSearch.value) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    let query = searchMode.value === 'text' 
        ? searchQuery.value.trim() 
        : `${coordinateForm.value.latitude},${coordinateForm.value.longitude}`;

    try {
        const { data } = await axios.post('/gorilla/search', {
            searchQuery: query,
            lang: selectedLanguage.value,
            filterData: selectedFilter.value,
        });

        searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));

        if (searchMode.value === 'coordinates') {
            const resultsArray = Array.isArray(data) ? data : (data ? [data] : []);
            searchResults.value.others = resultsArray.map(item => ({
                ...item,
                type: 'other',
                geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
            }));
        } else {
            for (const type of ALL_RESULT_TYPES) {
                if (data[type] && Array.isArray(data[type])) {
                    searchResults.value[type] = data[type].map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }));
                }
            }
        }
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
        updateMapLayers();
    }
}, MAP_CONFIG.debounceMs);

const submitCoordinateSearch = () => {
    if (canSearch.value) {
        performSearch();
    } else {
        error.value = "Please enter valid latitude (-90 to 90) and longitude (-180 to 180).";
    }
};

const clearSearch = (resetInputs = true) => {
    if (resetInputs) {
        searchMode.value === 'text' ? searchQuery.value = '' : (coordinateForm.value = { latitude: '', longitude: '' });
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
    clearSelection();
    updateMapLayers();
};

// --- MAP LAYER UPDATES ---
const updateMapLayers = () => {
    if (!map.value?.isStyleLoaded() || !isMapLoaded.value) return;

    smoothSwitchGeoJson(null); // Clear active GeoJSON

    // Clean up old search layers
    searchLayerIds.value.forEach(layerId => {
        if (map.value.getLayer(layerId)) map.value.removeLayer(layerId);
    });
    searchSourceIds.value.forEach(sourceId => {
        if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);
    });

    searchLayerIds.value = [];
    searchSourceIds.value = [];
    clearHighlight();

    // Hide/show province base layers
    const baseLayerVisibility = totalResults.value > 0 ? 'none' : 'visible';
    props.provinces.forEach(p => {
        if (p?.id && map.value.getSource(`province-source-${p.id}`)) {
            ['fill', 'border'].forEach(suffix => {
                const layerId = `province-${suffix}-${p.id}`;
                if (map.value.getLayer(layerId)) {
                    map.value.setLayoutProperty(layerId, 'visibility', baseLayerVisibility);
                }
            });
        }
    });

    const allFeaturesForZoom = [];
    
    // Add search results as points (NO default markers for coordinate search with GeoJSON)
    if (totalResults.value > 0) {
        ALL_RESULT_TYPES.forEach(type => {
            const featureType = type.endsWith('s') ? type.slice(0, -1) : type;
            searchResults.value[type].forEach(result => {
                // Only add point marker if no GeoJSON
                if (result.latitude && result.longitude && !result.geojson) {
                    const pointFeature = { 
                        type: "Feature", 
                        geometry: { type: "Point", coordinates: [result.longitude, result.latitude] }, 
                        properties: { ...result, id: result.id, type: featureType } 
                    };
                    addPointMarker(featureType, result.id, pointFeature, true);
                    allFeaturesForZoom.push(pointFeature);
                } else if (result.geojson) {
                    // Add GeoJSON bounds for zoom calculation
                    allFeaturesForZoom.push({
                        type: "Feature",
                        geometry: result.geojson,
                        properties: { ...result }
                    });
                }
            });
        });

        // HIDE default marker in coordinate mode (user will place custom marker on click)
        // Only show search center if NO results have GeoJSON
        const hasGeoJsonResults = searchResults.value.others.some(r => r.geojson);
        if (searchMode.value === 'coordinates' && canSearch.value && !hasGeoJsonResults) {
            // Remove this - no default marker
        }

        if (allFeaturesForZoom.length > 0) zoomToResults(allFeaturesForZoom);

    } else if (!canSearch.value) {
        map.value.flyTo({ center: MAP_CONFIG.center, zoom: MAP_CONFIG.zoom, duration: 1000 });
    }
};

const zoomToResults = (features) => {
    if (!map.value || features.length === 0) return;
    const bounds = new maplibregl.LngLatBounds();
    let hasValidGeometry = false;
    
    features.forEach(feature => {
        if (feature.geometry?.type === 'Point' && feature.geometry.coordinates) {
            bounds.extend(feature.geometry.coordinates);
            hasValidGeometry = true;
        } else if (feature.geometry?.bbox) {
            bounds.extend(feature.geometry.bbox);
            hasValidGeometry = true;
        } else if (feature.geometry?.coordinates) {
            const extendCoordinates = (coords) => {
                if (Array.isArray(coords[0])) coords.forEach(extendCoordinates);
                else bounds.extend(coords);
            };
            extendCoordinates(feature.geometry.coordinates);
            hasValidGeometry = true;
        }
    });
    
    if (hasValidGeometry && !bounds.isEmpty()) {
        map.value.fitBounds(bounds, { padding: MAP_CONFIG.fitBoundsPadding, maxZoom: MAP_CONFIG.maxZoomForFit, duration: 1000 });
    }
};

const selectResult = (type, id) => {
    const resultTypeKey = type.endsWith('s') ? type : `${type}s`;
    const result = searchResults.value[resultTypeKey]?.find(r => r.id === id);

    if (result) {
        if (result.geojson && result.key) {
            smoothSwitchGeoJson(result);
        } else {
            smoothSwitchGeoJson(null);
        }

        displayFeaturePopup(result, type);
        if (window.innerWidth < 640) {
            panelsVisible.value = false;
        }
    }
};

// --- MAP INTERACTION ---
const getUserLocation = () => {
    if (!navigator.geolocation) {
        error.value = "Geolocation is not supported by your browser.";
        return;
    }
    isLoading.value = true;
    navigator.geolocation.getCurrentPosition(
        (position) => {
            coordinateForm.value.latitude = position.coords.latitude.toFixed(6);
            coordinateForm.value.longitude = position.coords.longitude.toFixed(6);
            
            // Place custom marker at user location
            if (clickMarker) clickMarker.remove();
            clickMarker = new maplibregl.Marker({ color: '#1A773E' })
                .setLngLat([position.coords.longitude, position.coords.latitude])
                .addTo(map.value);
            
            submitCoordinateSearch();
        },
        (err) => {
            isLoading.value = false;
            error.value = `Failed to get location: ${err.message}`;
        }
    );
};

const handleMapClick = (e) => {
    if (!map.value) return;

    // Check if clicked within Rwanda bounds
    const { lat, lng } = e.lngLat;
    const isInRwanda = lat >= RWANDA_BOUNDS[0][1] && lat <= RWANDA_BOUNDS[1][1] && 
                       lng >= RWANDA_BOUNDS[0][0] && lng <= RWANDA_BOUNDS[1][0];

    if (!isInRwanda) {
        error.value = "Please select a location within Rwanda.";
        setTimeout(() => error.value = null, 3000);
        return;
    }

    const layersToQuery = [
        ...searchLayerIds.value.filter(id => map.value.getLayer(id) && (id.includes('-fill-') || id.includes('-point-'))),
        ...props.provinces.flatMap(p => [`province-fill-${p.id}`]).filter(id => map.value.getLayer(id))
    ];
    const features = map.value.queryRenderedFeatures(e.point, { layers: layersToQuery });

    if (features.length > 0) {
        const topFeature = features[0];
        const type = topFeature.properties.type;
        const id = topFeature.properties.id;
        
        if (id !== undefined && type) {
            const resultTypeKey = type.endsWith('s') ? type : `${type}s`;
            let result = searchResults.value[resultTypeKey]?.find(r => r.id == id);

            if (!result && type === 'province') {
                result = props.provinces.find(p => p.id == id);
                if (result) {
                    if (typeof result.geojson === 'string') result.geojson = JSON.parse(result.geojson);
                    if (typeof result.latitude === 'string') result.latitude = parseFloat(result.latitude);
                    if (typeof result.longitude === 'string') result.longitude = parseFloat(result.longitude);
                }
            }
            
            if (result) {
                selectResult(type, id);
                return;
            }
        }
    }

    // Place custom marker on map click in coordinate mode
    if (searchMode.value === 'coordinates') {
        coordinateForm.value.latitude = lat.toFixed(6);
        coordinateForm.value.longitude = lng.toFixed(6);
        
        if (clickMarker) clickMarker.remove();
        clickMarker = new maplibregl.Marker({ color: '#1A773E' })
            .setLngLat([lng, lat])
            .addTo(map.value);
        
        submitCoordinateSearch();
    }
};

// --- MAP LAYERS ---
const addGeoJsonLayer = (type, id, feature, trackLayer) => {
    if (!map.value || !feature?.geometry) return {};
    
    const sourceId = `${type}-source-${id}`;
    const fillLayerId = `${type}-fill-${id}`;
    const borderLayerId = `${type}-border-${id}`;
    
    if (map.value.getLayer(fillLayerId)) map.value.removeLayer(fillLayerId);
    if (map.value.getLayer(borderLayerId)) map.value.removeLayer(borderLayerId);
    if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);
    
    const layerStyle = UI_CONFIG.layerStyles[type] || UI_CONFIG.layerStyles.searchResult;
    
    map.value.addSource(sourceId, { type: 'geojson', data: feature, promoteId: 'id' });
    
    map.value.addLayer({
        id: fillLayerId, 
        type: 'fill', 
        source: sourceId,
        paint: { 
            'fill-color': layerStyle.fillColor, 
            'fill-opacity': 0 // Start at 0 for fade in
        },
    });
    
    map.value.addLayer({
        id: borderLayerId, 
        type: 'line', 
        source: sourceId,
        paint: { 
            'line-color': layerStyle.borderColor, 
            'line-width': layerStyle.borderWidth,
            'line-opacity': 0 // Start at 0 for fade in
        },
    });
    
    if (trackLayer) {
        searchLayerIds.value.push(fillLayerId, borderLayerId);
        searchSourceIds.value.push(sourceId);
    }

    return { fillId: fillLayerId, borderId: borderLayerId, sourceId };
};

const addPointMarker = (type, id, feature, trackLayer) => {
    if (!map.value || !feature?.geometry) return;
    
    const sourceId = `${type}-point-source-${id}`;
    const layerId = `${type}-point-${id}`;
    
    if (map.value.getLayer(layerId)) map.value.removeLayer(layerId);
    if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);
    
    const layerStyle = UI_CONFIG.layerStyles[type] || UI_CONFIG.layerStyles.search_center;
    
    map.value.addSource(sourceId, { type: 'geojson', data: feature, promoteId: 'id' });
    
    map.value.addLayer({
        id: layerId, 
        type: 'circle', 
        source: sourceId,
        paint: {
            'circle-color': layerStyle.fillColor || '#1A773E',
            'circle-radius': ['case', ['boolean', ['feature-state', 'hover'], false], 8, 6],
            'circle-stroke-color': layerStyle.borderColor || '#FFFFFF',
            'circle-stroke-width': ['case', ['boolean', ['feature-state', 'hover'], false], 2, 1.5],
            'circle-opacity': 0.9,
        },
    });
    
    if (trackLayer) {
        searchLayerIds.value.push(layerId);
        searchSourceIds.value.push(sourceId);
    }
};

// --- MAP SETUP ---
const applyMapStyle = (themeName) => {
    if (!map.value || !map.value.isStyleLoaded()) return;
    const theme = UI_CONFIG.mapThemes[themeName];
    if (theme) {
        const source = map.value.getSource('osm');
        if (source) {
            source.setTiles([theme.url]);
        }
        selectedTheme.value = themeName;
        localStorage.setItem('mapTheme', themeName);
    }
};

onMounted(() => {
    if (!mapContainer.value) {
        error.value = 'Map container element not found.';
        return;
    }
    
    const initialTheme = UI_CONFIG.mapThemes[selectedTheme.value] || UI_CONFIG.mapThemes['Default'];

    try {
        map.value = new maplibregl.Map({
            container: mapContainer.value,
            style: {
                version: 8,
                sources: { 
                    osm: { 
                        type: 'raster', 
                        tiles: [initialTheme.url], 
                        tileSize: 256, 
                        attribution: initialTheme.attribution 
                    } 
                },
                layers: [{ 
                    id: 'osm-tiles', 
                    type: 'raster', 
                    source: 'osm', 
                    paint: initialTheme.paint 
                }],
            },
            center: MAP_CONFIG.center,
            zoom: MAP_CONFIG.zoom,
            attributionControl: false,
            maxBounds: RWANDA_BOUNDS, // Restrict to Rwanda
            maxBoundsViscosity: 1.0,
        });
        
        map.value.addControl(new maplibregl.NavigationControl(), 'top-right');
        map.value.addControl(new maplibregl.AttributionControl({ compact: true }), 'bottom-right');
        
        map.value.on('load', () => {
            isMapLoaded.value = true;
            setupMap();
        });
        
        map.value.on('click', handleMapClick);
        window.addEventListener('resize', () => map.value?.resize());
    } catch (err) {
        console.error('Map initialization error:', err);
        error.value = 'Failed to initialize the map.';
    }
});

onUnmounted(() => {
    window.removeEventListener('resize', () => map.value?.resize());
    map.value?.remove();
});

const setupMap = () => {
    if (!map.value) return;
    
    // Add provinces
    if (Array.isArray(props.provinces)) {
        props.provinces.forEach(province => {
            if (province?.id && province.geojson) {
                const feature = {
                    type: "Feature",
                    geometry: typeof province.geojson === 'string' ? JSON.parse(province.geojson) : province.geojson,
                    properties: { ...province, id: province.id, type: 'province' }
                };
                if (typeof feature.properties.latitude === 'string') feature.properties.latitude = parseFloat(feature.properties.latitude);
                if (typeof feature.properties.longitude === 'string') feature.properties.longitude = parseFloat(feature.properties.longitude);
                addGeoJsonLayer('province', province.id, feature, false);
            }
        });
    }
    
    // Add highlight layer
    map.value.addSource(UI_CONFIG.highlightSourceId, { 
        type: 'geojson', 
        data: { type: 'FeatureCollection', features: [] } 
    });
    
    map.value.addLayer({
        id: UI_CONFIG.highlightLayerId,
        type: 'line',
        source: UI_CONFIG.highlightSourceId,
        paint: { 
            'line-color': UI_CONFIG.highlightStyle.color, 
            'line-width': UI_CONFIG.highlightStyle.width, 
            'line-opacity': UI_CONFIG.highlightStyle.opacity 
        },
    });
};

const displayFeaturePopup = (feature, type) => {
    if (!map.value) return;
    clearSelection();
    
    selectedResultId.value = feature.id;
    selectedResultType.value = type;
    
    const highlightSource = map.value.getSource(UI_CONFIG.highlightSourceId);
    if (highlightSource) {
        if (feature.geojson) highlightSource.setData(feature.geojson);
        else highlightSource.setData({ type: 'FeatureCollection', features: [] });
    }
    
    const displayName = getDisplayName(feature);
    const popupContent = `
        <div class="selected-popup">
            <h3>${displayName}</h3>
            <p class="popup-type">${type.charAt(0).toUpperCase() + type.slice(1)}</p>
            ${feature.address ? `<p class="popup-address">${feature.address}</p>` : ''}
            ${feature.code ? `<p class="popup-code">Code: ${feature.code}</p>` : ''}
            ${typeof feature.latitude === 'number' && typeof feature.longitude === 'number' ? `<p class="popup-coords">Lat: