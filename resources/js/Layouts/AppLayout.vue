<script setup>
import { ref } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import Banner from '@/Components/Banner.vue';
import {
    Search,
    Navigation,
    FileText,
    Map,
    User,
    Settings,
    LogOut,
    LogIn,
    UserPlus,
    X,
    Home,
    Menu,
    MapPin,
} from 'lucide-vue-next';

defineProps({
    title: String,
});

// Navigation state management
const showingPopupMenu = ref(false);
const mobileMenuOpen = ref(false);

const logout = () => {
    router.post(route('logout'));
    showingPopupMenu.value = false;
};

// Popup menu functions
const closePopupMenu = () => {
    showingPopupMenu.value = false;
};

const togglePopupMenu = () => {
    showingPopupMenu.value = !showingPopupMenu.value;
    mobileMenuOpen.value = false;
};

const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value;
    showingPopupMenu.value = false;
};
</script>

<template>
    <div>
        <Head :title="title" />
        <Banner />

        <div class="min-h-screen bg-cta-background-two">
            <!-- Navigation Header -->
            <nav class="bg-white border-b border-gray-200 sticky top-0 z-50">
                <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex items-center justify-between h-16">
                        <!-- Logo -->
                        <div class="flex items-center space-x-3">
                            <Link :href="route('landing')" class="flex items-center group">
                                <div class="w-10 h-10 bg-gorilla-primary rounded-xl flex items-center justify-center">
                                    <MapPin class="w-6 h-6 text-white" />
                                </div>
                                <span class="ml-2 text-xl font-bold text-gorilla-primary-three hidden sm:inline">Rwanda Geo</span>
                            </Link>
                        </div>

                        <!-- Desktop Navigation -->
                        <div class="hidden md:flex items-center space-x-8">
                            <Link :href="route('search')"
                                class="text-sm text-gorilla-primary-three hover:text-gorilla-primary transition-colors"
                                :class="route().current('search') ? 'font-semibold text-gorilla-primary' : ''">
                                Search
                            </Link>
                           
                            <Link :href="route('mapApi.search')"
                                class="text-sm text-gorilla-primary-three hover:text-gorilla-primary transition-colors"
                                :class="route().current('mapApi.search') ? 'font-semibold text-gorilla-primary' : ''">
                                Documentation
                            </Link>
                            <Link :href="route('myMap.index')"
                                class="text-sm text-gorilla-primary-three hover:text-gorilla-primary transition-colors"
                                :class="route().current('myMap.index') ? 'font-semibold text-gorilla-primary' : ''">
                                My Maps
                            </Link>
                            <button @click="togglePopupMenu"
                                class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Account
                            </button>
                        </div>

                        <!-- Mobile Menu Button -->
                        <button @click="toggleMobileMenu" class="md:hidden">
                            <Menu v-if="!mobileMenuOpen" class="h-6 w-6 text-gorilla-primary-three" />
                            <X v-else class="h-6 w-6 text-gorilla-primary-three" />
                        </button>
                    </div>

                    <!-- Mobile Navigation -->
                    <div v-if="mobileMenuOpen" class="md:hidden py-4 border-t border-gray-200">
                        <div class="flex flex-col space-y-3">
                            <Link :href="route('search')"
                                class="text-sm text-gorilla-primary-three hover:text-gorilla-primary transition-colors py-2"
                                :class="route().current('search') ? 'font-semibold text-gorilla-primary' : ''"
                                @click="mobileMenuOpen = false">
                                Search
                            </Link>
                            <Link :href="route('navigation')"
                                class="text-sm text-gorilla-primary-three hover:text-gorilla-primary transition-colors py-2"
                                :class="route().current('navigation') ? 'font-semibold text-gorilla-primary' : ''"
                                @click="mobileMenuOpen = false">
                                Navigation
                            </Link>
                            <!-- <Link :href="route('mapApi.search')"
                                class="text-sm text-gorilla-primary-three hover:text-gorilla-primary transition-colors py-2"
                                :class="route().current('mapApi.search') ? 'font-semibold text-gorilla-primary' : ''"
                                @click="mobileMenuOpen = false">
                                Documentation
                            </Link> -->
                            <Link :href="route('myMap.index')"
                                class="text-sm text-gorilla-primary-three hover:text-gorilla-primary transition-colors py-2"
                                :class="route().current('myMap.index') ? 'font-semibold text-gorilla-primary' : ''"
                                @click="mobileMenuOpen = false">
                                My Maps
                            </Link>
                            <button @click="togglePopupMenu"
                                class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white w-full py-2 rounded-lg text-sm font-medium transition-colors">
                                Account
                            </button>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- User Menu Popup -->
            <div v-if="showingPopupMenu"
                class="fixed inset-0 bg-black bg-opacity-30 z-40 transition-opacity duration-300"
                @click="closePopupMenu"></div>

            <div v-if="showingPopupMenu" class="fixed inset-0 flex items-center justify-center z-50 p-4">
                <div :class="{ 'scale-100 opacity-100': showingPopupMenu, 'scale-95 opacity-0': !showingPopupMenu }"
                    class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 border border-gray-100">
                    <div class="p-6 md:p-8">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-6 md:mb-8">
                            <h2 class="text-xl md:text-2xl font-bold text-white">Menu</h2>
                            <button @click="closePopupMenu"
                                class="p-2 rounded-xl hover:bg-cta-background-one transition-colors">
                                <X class="w-5 h-5 md:w-6 md:h-6 text-gorilla-primary-three" />
                            </button>
                        </div>

                        <!-- User Section - Not Logged In -->
                        <div v-if="$page.props.auth.user === null" class="mb-6 md:mb-8 p-4 md:p-6  rounded-xl text-center">
                            <div class="mb-4 md:mb-6">
                                <div class="w-12 h-12 md:w-16 md:h-16 bg-gorilla-primary/10 rounded-full flex items-center justify-center mx-auto mb-3 md:mb-4">
                                    <User class="w-6 h-6 md:w-8 md:h-8 text-gorilla-primary" />
                                </div>
                                <p class="text-gorilla-primary-three font-semibold text-base md:text-lg">Join OnRwanda Geo</p>
                                <p class="text-gray-600 text-xs md:text-sm mt-2">Access premium mapping features and save your locations</p>
                            </div>
                            <div class="space-y-2 md:space-y-3">
                                <Link :href="route('login')"
                                    class="w-full flex items-center justify-center space-x-2 px-4 md:px-6 py-2.5 md:py-3 bg-gorilla-primary text-white rounded-xl hover:bg-gorilla-primary/90 transition-all duration-200 font-medium text-sm md:text-base"
                                    @click="closePopupMenu">
                                    <LogIn class="w-4 h-4 md:w-5 md:h-5" />
                                    <span>Login</span>
                                </Link>
                                <Link :href="route('register')"
                                    class="w-full flex items-center justify-center space-x-2 px-4 md:px-6 py-2.5 md:py-3 bg-white text-gray-700 border border-gray-300 rounded-xl hover:bg-gray-50 hover:text-gorilla-primary-three transition-all duration-200 font-medium text-sm md:text-base"
                                    @click="closePopupMenu">
                                    <UserPlus class="w-4 h-4 md:w-5 md:h-5" />
                                    <span>Register</span>
                                </Link>
                            </div>
                        </div>

                        <!-- User Section - Logged In -->
                        <div v-if="$page.props.auth.user !== null" class="mb-6 md:mb-8 p-4 md:p-6 bg-cta-background-one rounded-xl text-center">
                            <div class="mb-4">
                                <img v-if="$page.props.jetstream.managesProfilePhotos"
                                    :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name"
                                    class="w-12 h-12 md:w-16 md:h-16 rounded-full object-cover mx-auto mb-2 md:mb-3 border-2 border-gorilla-primary/20" />
                                <div v-else class="w-12 h-12 md:w-16 md:h-16 bg-gorilla-primary rounded-full flex items-center justify-center mx-auto mb-2 md:mb-3">
                                    <User class="w-6 h-6 md:w-8 md:h-8 text-white" />
                                </div>
                                <div>
                                    <p class="font-bold text-gorilla-primary-three text-base md:text-lg">{{ $page.props.auth.user.name }}</p>
                                    <p class="text-gray-600 text-xs md:text-sm truncate px-2">{{ $page.props.auth.user.email }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Links -->
                        <nav class="space-y-2 mb-6 md:mb-8">
                           

                            <!-- User Account Links -->
                            <template v-if="$page.props.auth.user !== null">
                                <div>
                                    <h3 class="text-xs md:text-sm font-semibold text-gorilla-primary-three mb-2 md:mb-3 px-2">Account</h3>
                                    <div class="space-y-1">
                                        <Link :href="route('dashboard')"
                                            class="flex items-center space-x-3 px-3 md:px-4 py-2.5 md:py-3 rounded-xl transition-all duration-200 font-medium group text-sm md:text-base"
                                            :class="route().current('dashboard')
                                                ? 'bg-gorilla-primary text-white'
                                                : 'text-gorilla-primary-three hover:bg-cta-background-one'"
                                            @click="closePopupMenu">
                                            <div class="p-1.5 rounded-lg transition-colors"
                                                :class="route().current('dashboard')
                                                    ? 'bg-white/20'
                                                    : 'bg-gray-100 group-hover:bg-gray-200'">
                                                <Home class="w-4 h-4" />
                                            </div>
                                            <span>Dashboard</span>
                                        </Link>

                                        <Link :href="route('profile.show')"
                                            class="flex items-center space-x-3 px-3 md:px-4 py-2.5 md:py-3 rounded-xl transition-all duration-200 font-medium group text-sm md:text-base"
                                            :class="route().current('profile.show')
                                                ? 'bg-gorilla-primary text-white'
                                                : 'text-gorilla-primary-three hover:bg-cta-background-one'"
                                            @click="closePopupMenu">
                                            <div class="p-1.5 rounded-lg transition-colors"
                                                :class="route().current('profile.show')
                                                    ? 'bg-white/20'
                                                    : 'bg-gray-100 group-hover:bg-gray-200'">
                                                <User class="w-4 h-4" />
                                            </div>
                                            <span>Profile Settings</span>
                                        </Link>

                                        <Link v-if="$page.props.jetstream.hasApiFeatures" :href="route('api-tokens.index')"
                                            class="flex items-center space-x-3 px-3 md:px-4 py-2.5 md:py-3 rounded-xl transition-all duration-200 font-medium group text-sm md:text-base"
                                            :class="route().current('api-tokens.index')
                                                ? 'bg-gorilla-primary text-white'
                                                : 'text-gorilla-primary-three hover:bg-cta-background-one'"
                                            @click="closePopupMenu">
                                            <div class="p-1.5 rounded-lg transition-colors"
                                                :class="route().current('api-tokens.index')
                                                    ? 'bg-white/20'
                                                    : 'bg-gray-100 group-hover:bg-gray-200'">
                                                <Settings class="w-4 h-4" />
                                            </div>
                                            <span>API Tokens</span>
                                        </Link>
                                    </div>
                                </div>
                            </template>
                        </nav>

                        <!-- Logout Button -->
                        <div v-if="$page.props.auth.user !== null" class="pt-4 md:pt-6 border-t border-gray-100">
                            <button @click="logout"
                                class="w-full flex items-center justify-center space-x-3 px-4 md:px-6 py-2.5 md:py-3 bg-red-50 text-red-600 rounded-xl hover:bg-red-100 transition-all duration-200 font-medium text-sm md:text-base">
                                <LogOut class="w-4 h-4 md:w-5 md:h-5" />
                                <span>Log Out</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <main class="bg-cta-background-two min-h-screen">
                <slot />
            </main>
        </div>
    </div>
</template>

<style scoped>
/* Custom scrollbar for popup menu */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: #edefeb;
    border-radius: 2px;
}

::-webkit-scrollbar-thumb {
    background: #1A773E;
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1C5172;
}

/* Focus states for accessibility */
button:focus,
a:focus {
    outline: 2px solid #1A773E;
    outline-offset: 2px;
}
</style>