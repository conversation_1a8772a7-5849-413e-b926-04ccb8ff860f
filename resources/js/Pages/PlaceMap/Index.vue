<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import { ref, onMounted, computed, defineComponent, h, watch } from 'vue';
import axios from 'axios';
import { <PERSON>, <PERSON>O<PERSON>, CircleCheck, CircleX } from 'lucide-vue-next';

// --- Icon Component ---
const MapIcon = defineComponent({
    props: ['name', 'size'],
    setup(props) {
        const size = props.size || 20;
        
        const iconPaths = {
            pin: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/><circle cx="12" cy="9" r="2.5"/>`,
            star: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
            heart: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
            flag: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
            home: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/>`,
            work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/><line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
            cafe: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z" stroke="currentColor" stroke-width="2" fill="none"/>`,
            park: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 22v-6m0 0l-3-3m3 3l3-3m-3-10v10m0-10l-3-3m3 3l3-3M12 2v10"/><path d="M5 12h14" stroke="currentColor" stroke-width="2"/>`,
            restaurant: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
            shopping: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
            hospital: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6"/><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            school: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,
            search: `<circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2" fill="none"/><path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>`,
            x: `<path d="M18 6 6 18M6 6l12 12" stroke="currentColor" stroke-width="2"/>`,
            chevronLeft: `<polyline points="15,18 9,12 15,6" stroke="currentColor" stroke-width="2" fill="none"/>`,
            chevronRight: `<polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" fill="none"/>`,
            chevronsLeft: `<polyline points="11,17 6,12 11,7" stroke="currentColor" stroke-width="2" fill="none"/><polyline points="18,17 13,12 18,7" stroke="currentColor" stroke-width="2" fill="none"/>`,
            chevronsRight: `<polyline points="13,17 18,12 13,7" stroke="currentColor" stroke-width="2" fill="none"/><polyline points="6,17 11,12 6,7" stroke="currentColor" stroke-width="2" fill="none"/>`,
            itinerary: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 3.34a10 10 0 1 1-14.995 2.898M7 10h4m-2-2v4m5-6a2 2 0 1 1 4 0 2 2 0 0 1-4 0Z"/>`,
            administration: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 22V10m0-8v8m0 0-3-3m3 3 3-3m5 12v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>`,
            tracking: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Zm0-4a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z"/>`,
        };

        return () => h('svg', {
            width: size,
            height: size,
            viewBox: '0 0 24 24',
            fill: 'none',
            innerHTML: iconPaths[props.name] || iconPaths.pin
        });
    }
});

// --- Components ---

// Reusable Alert Component
const Alert = defineComponent({
    props: ['type', 'message'],
    emits: ['close'],
    setup(props, { emit }) {
        const alertClasses = computed(() => {
            const base = 'fixed bottom-6 left-6 z-[100] max-w-sm p-4 rounded-lg border-2  flex justify-between items-center';
            if (props.type === 'success') return `${base} bg-green-50 text-green-900 border-green-400`;
            if (props.type === 'error') return `${base} bg-red-50 text-red-900 border-red-400`;
            return `${base} bg-blue-50 text-blue-900 border-blue-400`;
        });

        return () => h('div', { class: alertClasses.value, role: 'alert' }, [
            h('span', { class: 'font-medium text-sm' }, props.message),
            h('button', {
                type: 'button',
                class: 'ml-4 -mt-1 font-bold text-2xl hover:text-gray-700',
                onClick: () => emit('close')
            }, '×')
        ]);
    }
});

// Preloader Component
const Preloader = defineComponent({
    setup() {
        return () => h('div', { class: 'absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50' }, [
            h('div', { class: 'animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500' })
        ]);
    }
});

// Shadcn-like component classes
const Button = 'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';
const ButtonPrimary = `${Button} bg-black text-white hover:bg-gray-800 `;
const ButtonSecondary = `${Button} bg-gray-200 text-gray-800 hover:bg-gray-300`;
const ButtonDestructive = `${Button} bg-red-600 text-white hover:bg-red-700`;

const Input = 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50';
const Label = 'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70';
const Select = `${Input}`;

// --- Props ---
const props = defineProps({
    placeMaps: {
        type: Object,
        default: () => ({ data: [], links: {}, meta: {} })
    },
    placeMapTypes : Array,
});

// --- State ---
const placeMaps = ref([]);
const pagination = ref({});
const searchQuery = ref('');
const searchDebounceTimeout = ref(null);
const perPage = ref(10);
const perPageOptions = ref([5, 10, 25, 50]);
const loading = ref(false);
const alert = ref({ show: false, type: '', message: '' });

const showCreateEditModal = ref(false);
const isEditing = ref(false);
const editingPlaceMapId = ref(null);
const modalTab = ref('main'); // 'main' or 'customFields'
const showShareModal = ref(false);
const shareableUrl = ref('');

const getInitialForm = () => ({
    name: '',
    description: '',
    type: 'general',
    image: 'pin', // Default icon
    visibility: 'private',
    status: 'active',
    zoom: 12,
    latitude: null,
    longitude: null,
    isTemplate: 'no',
    customFields: [],
    processing: false,
    errors: {}
});

const form = ref(getInitialForm());

const mapIcons = ref([
    { name: 'pin', label: 'Location Pin' },
    { name: 'star', label: 'Star' },
    { name: 'heart', label: 'Heart' },
    { name: 'flag', label: 'Flag' },
    { name: 'home', label: 'Home' },
    { name: 'work', label: 'Work' },
    { name: 'cafe', label: 'Cafe' },
    { name: 'park', label: 'Park' },
    { name: 'restaurant', label: 'Restaurant' },
    { name: 'shopping', label: 'Shopping' },
    { name: 'hospital', label: 'Hospital' },
    { name: 'school', label: 'School' }
]);

const mapTypes = ref([
    { value: 'itinerary', label: 'Itinerary', icon: 'itinerary' },
    { value: 'administration', label: 'Administration', icon: 'administration' },
    { value: 'general', label: 'General', icon: 'pin' },
    { value: 'tracking', label: 'Tracking', icon: 'tracking' }
]);

// --- Functions ---
const showAlert = (type, message, duration = 4000) => {
    alert.value = { show: true, type, message };
    setTimeout(() => alert.value.show = false, duration);
};

const openShareModal = (placeMap) => {
    if (placeMap.key) {
        shareableUrl.value = `${window.location.origin}/map/shared/${placeMap.key}`;
        showShareModal.value = true;
    } else {
        showAlert('error', 'This map does not have a share key.');
    }
};

const copyToClipboard = () => {
    navigator.clipboard.writeText(shareableUrl.value).then(() => {
        showAlert('success', 'Link copied to clipboard!');
    }, () => {
        showAlert('error', 'Failed to copy link.');
    });
    showShareModal.value = false;
};

const fetchPlaceMaps = async (page = 1, perPageValue = perPage.value) => {
    loading.value = true;
    try {
        const response = await axios.get(route('placeMap.index', {
            page,
            searchQuery: searchQuery.value || null,
            perPage: perPageValue
        }));
        placeMaps.value = response.data.data;
        pagination.value = {
            currentPage: response.data.currentPage,
            lastPage: response.data.lastPage,
            total: response.data.total,
            perPage: response.data.itemsPerPage,
        };
        perPage.value = perPageValue;
    } catch (error) {
        showAlert('error', 'Error fetching place maps.');
        console.error('Error fetching place maps:', error);
    } finally {
        loading.value = false;
    }
};

// Watch for search query changes and debounce
watch(searchQuery, (newQuery) => {
    if (searchDebounceTimeout.value) {
        clearTimeout(searchDebounceTimeout.value);
    }
    
    searchDebounceTimeout.value = setTimeout(() => {
        if (newQuery.length >= 3 || newQuery.length === 0) {
            fetchPlaceMaps(1, perPage.value);
        }
    }, 500);
});

const clearSearch = () => {
    searchQuery.value = '';
    fetchPlaceMaps(1, perPage.value);
};

const goToPage = (page) => {
    if (page >= 1 && page <= pagination.value.lastPage && !loading.value) {
        fetchPlaceMaps(page, perPage.value);
    }
};

// Generate pagination pages array
const paginationPages = computed(() => {
    const current = pagination.value.currentPage;
    const last = pagination.value.lastPage;
    const pages = [];
    
    if (last <= 7) {
        for (let i = 1; i <= last; i++) pages.push(i);
    } else {
        if (current <= 4) {
            for (let i = 1; i <= 5; i++) pages.push(i);
            pages.push('...');
            pages.push(last);
        } else if (current >= last - 3) {
            pages.push(1);
            pages.push('...');
            for (let i = last - 4; i <= last; i++) pages.push(i);
        } else {
            pages.push(1);
            pages.push('...');
            for (let i = current - 1; i <= current + 1; i++) pages.push(i);
            pages.push('...');
            pages.push(last);
        }
    }
    
    return pages;
});

onMounted(() => {
    fetchPlaceMaps();
});

const openCreateModal = () => {
    isEditing.value = false;
    editingPlaceMapId.value = null;
    form.value = getInitialForm();
    modalTab.value = 'main';
    showCreateEditModal.value = true;
};

const openEditModal = (placeMap) => {
    isEditing.value = true;
    editingPlaceMapId.value = placeMap.id;
    form.value = {
        name: placeMap.name,
        type: placeMap.type,
        description: placeMap.description,
        image: placeMap.image || 'pin',
        visibility: placeMap.visibility || 'private',
        status: placeMap.status || 'active',
        zoom: placeMap.zoom || 12,
        latitude: placeMap.latitude,
        longitude: placeMap.longitude,
        isTemplate: placeMap.isTemplate || 'no',
        customFields: placeMap.customFields ? JSON.parse(placeMap.customFields) : [],
        processing: false,
        errors: {}
    };
    modalTab.value = 'main';
    showCreateEditModal.value = true;
};

const addCustomField = () => {
    form.value.customFields.push({ name: '', type: 'text', length: 255, isRequired: 'no', isUnique: 'no' });
};

const removeCustomField = (index) => {
    form.value.customFields.splice(index, 1);
};

const submitPlaceMap = async () => {
    form.value.processing = true;
    form.value.errors = {};
    const url = isEditing.value
        ? route('placeMap.update', { placeMapId: editingPlaceMapId.value })
        : route('placeMap.store');
    
    try {
        const response = await axios.post(url, form.value);
        showAlert('success', response.data.message);
        showCreateEditModal.value = false;
        fetchPlaceMaps(pagination.value.currentPage, perPage.value);
    } catch (error) {
        const errorMessage = error.response?.data?.message || 'An unexpected error occurred.';
        showAlert('error', errorMessage);
        if (error.response && error.response.status === 422 && error.response.data.errors) {
            form.value.errors = error.response.data.errors;
        } else {
            console.error('An unexpected error occurred:', error);
        }
    } finally {
        form.value.processing = false;
    }
};
</script>

<template>
    <AppLayout title="My Maps">
        <Alert v-if="alert.show" :type="alert.type" :message="alert.message" @close="alert.show = false" />
        <div class="py-12 bg-cta-background-two min-h-screen">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="overflow-hidden">
                    <div class="flex flex-col md:flex-row justify-between md:items-center mb-8 gap-4">
                        <h1 class="text-3xl font-bold text-gorilla-primary-three">My Maps</h1>
                        <button @click="openCreateModal" class="inline-flex items-center px-6 py-3 bg-gorilla-primary text-white rounded-xl hover:bg-gorilla-primary/90 transition-colors font-medium">Create New Map</button>
                    </div>

                    <div class="mb-8">
                        <div class="relative max-w-md">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <MapIcon name="search" :size="20" class="text-gray-400" />
                            </div>
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Search maps..."
                                class="flex h-10 w-full rounded-xl border border-gray-300 bg-white px-3 py-2 pl-10 pr-10 text-sm focus:border-gorilla-primary focus:outline-none focus:ring-2 focus:ring-gorilla-primary/20"
                            />
                            <button
                                v-if="searchQuery"
                                @click="clearSearch"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gorilla-primary-three transition-colors"
                                title="Clear search"
                            >
                                <MapIcon name="x" :size="18" />
                            </button>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">
                            {{ searchQuery.length >= 3 ? `Searching for "${searchQuery}"...` : 'Type at least 3 characters to search' }}
                        </p>
                    </div>

                    <div class="relative">
                        <Preloader v-if="loading" />
                        <div v-if="!loading && placeMaps.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div v-for="(placeMap, index) in placeMaps" :key="placeMap.id" class="bg-white border border-gray-200 rounded-xl hover:border-gorilla-primary/30 transition-all duration-300 flex flex-col">
                                <div class="p-4 border-b border-gray-200 flex items-start gap-4">
                                    <div class="flex-shrink-0 w-12 h-12 bg-cta-background-one rounded-xl flex items-center justify-center">
                                        <MapIcon :name="placeMap.image || 'pin'" :size="28" class="text-gorilla-primary-three" />
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center">
                                            <span class="text-sm font-bold text-gray-500 mr-2">{{ ((pagination.currentPage - 1) * pagination.perPage) + index + 1 }}.</span>
                                            <h3 class="text-lg font-bold text-gorilla-primary-three truncate">{{ placeMap.name }}</h3>
                                        </div>
                                        <div class="flex items-center gap-2 mt-2 flex-wrap">
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium capitalize"
                                                  :class="placeMap.visibility === 'public' ? 'bg-gorilla-primary/10 text-gorilla-primary' : 'bg-gray-100 text-gray-800'">
                                                {{ placeMap.visibility }}
                                            </span>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium capitalize"
                                                  :class="placeMap.status === 'active' ? 'bg-gorilla-primary-two/10 text-gorilla-primary-two' : 'bg-red-100 text-red-800'">
                                                {{ placeMap.status }}
                                            </span>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium capitalize bg-gorilla-primary-three/10 text-gorilla-primary-three">
                                                {{ placeMap.type }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 flex-grow">
                                    <p class="text-sm text-gray-600 leading-relaxed h-20 overflow-hidden">
                                        {{ placeMap.description || 'No description provided.' }}
                                    </p>
                                </div>
                                <div class="p-3 bg-cta-background-one border-t flex justify-end items-center gap-3">
                                    <Link :href="route('myMap.show', { placeMapId: placeMap.id })"
                                          class="inline-flex items-center justify-center rounded-xl text-sm font-medium bg-white text-gorilla-primary-three hover:bg-gray-50 border border-gray-300 px-3 py-1.5">
                                        Manage
                                    </Link>
                                    <button @click="openEditModal(placeMap)"
                                            class="inline-flex items-center justify-center rounded-xl text-sm font-medium bg-white text-gorilla-primary-three hover:bg-gray-50 border border-gray-300 px-3 py-1.5">
                                        Edit
                                    </button>
                                    <button @click="openShareModal(placeMap)"
                                            class="inline-flex items-center justify-center rounded-xl text-sm font-medium bg-gorilla-primary text-white hover:bg-gorilla-primary/90 px-3 py-1.5">
                                        Share
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div v-if="!loading && placeMaps.length === 0" class="text-center text-gray-500 py-16">
                            <div class="w-16 h-16 bg-cta-background-one rounded-xl flex items-center justify-center mx-auto mb-4">
                                <MapIcon name="pin" :size="32" class="text-gray-400" />
                            </div>
                            <p class="text-xl font-semibold text-gorilla-primary-three">{{ searchQuery ? 'No maps found matching your search.' : 'No maps found.' }}</p>
                            <p class="text-md mt-2">{{ searchQuery ? 'Try adjusting your search terms.' : 'Get started by creating a new map.' }}</p>
                        </div>
                    </div>

                    <!-- Modern Pagination -->
                    <div v-if="pagination.total > 0" class="mt-12">
                        <div class="bg-white border border-gray-200 rounded-xl p-6">
                            <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                                <div class="flex items-center gap-3">
                                    <label for="perPage" class="text-sm font-medium text-gorilla-primary-three">Show:</label>
                                    <select
                                        id="perPage"
                                        v-model="perPage"
                                        @change="fetchPlaceMaps(1, $event.target.value)"
                                        class="border border-gray-300 rounded-xl px-3 py-1.5 text-sm bg-white focus:border-gorilla-primary focus:ring-1 focus:ring-gorilla-primary/20"
                                    >
                                        <option v-for="option in perPageOptions" :key="option" :value="option">{{ option }}</option>
                                    </select>
                                    <span class="text-sm text-gray-600">per page</span>
                                </div>

                                <nav v-if="pagination.lastPage > 1" class="flex items-center">
                                    <div class="flex items-center gap-1">
                                        <button @click="goToPage(1)" :disabled="pagination.currentPage === 1 || loading" class="inline-flex items-center justify-center w-9 h-9 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-xl hover:bg-cta-background-one hover:text-gorilla-primary-three disabled:opacity-50 disabled:cursor-not-allowed" title="First page">
                                            <MapIcon name="chevronsLeft" :size="16" />
                                        </button>
                                        <button @click="goToPage(pagination.currentPage - 1)" :disabled="pagination.currentPage === 1 || loading" class="inline-flex items-center justify-center w-9 h-9 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-xl hover:bg-cta-background-one hover:text-gorilla-primary-three disabled:opacity-50 disabled:cursor-not-allowed" title="Previous page">
                                            <MapIcon name="chevronLeft" :size="16" />
                                        </button>
                                        <template v-for="page in paginationPages" :key="page">
                                            <button v-if="page !== '...'" @click="goToPage(page)" :class="['inline-flex items-center justify-center w-9 h-9 text-sm font-medium border rounded-xl', page === pagination.currentPage ? 'bg-gorilla-primary text-white border-gorilla-primary' : 'text-gray-500 bg-white border-gray-300 hover:bg-cta-background-one hover:text-gorilla-primary-three']">
                                                {{ page }}
                                            </button>
                                            <span v-else class="inline-flex items-center justify-center w-9 h-9 text-sm text-gray-500">...</span>
                                        </template>
                                        <button @click="goToPage(pagination.currentPage + 1)" :disabled="pagination.currentPage === pagination.lastPage || loading" class="inline-flex items-center justify-center w-9 h-9 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-xl hover:bg-cta-background-one hover:text-gorilla-primary-three disabled:opacity-50 disabled:cursor-not-allowed" title="Next page">
                                            <MapIcon name="chevronRight" :size="16" />
                                        </button>
                                        <button @click="goToPage(pagination.lastPage)" :disabled="pagination.currentPage === pagination.lastPage || loading" class="inline-flex items-center justify-center w-9 h-9 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-xl hover:bg-cta-background-one hover:text-gorilla-primary-three disabled:opacity-50 disabled:cursor-not-allowed" title="Last page">
                                            <MapIcon name="chevronsRight" :size="16" />
                                        </button>
                                    </div>
                                </nav>

                                <div class="text-sm text-gray-600">
                                    Showing {{ ((pagination.currentPage - 1) * pagination.perPage) + 1 }} to 
                                    {{ Math.min(pagination.currentPage * pagination.perPage, pagination.total) }} of 
                                    {{ pagination.total }} results
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Share Modal -->
        <div v-if="showShareModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-xl w-full max-w-md">
                <div class="p-6 border-b">
                    <h2 class="text-2xl font-bold text-gorilla-primary-three">Share Map</h2>
                </div>
                <div class="p-6">
                    <p class="text-sm text-gray-600 mb-4">Anyone with this link can view the map.</p>
                    <div class="flex items-center space-x-2">
                        <input :value="shareableUrl" readonly class="flex h-10 w-full rounded-xl border border-gray-300 bg-white px-3 py-2 text-sm focus:border-gorilla-primary focus:outline-none focus:ring-2 focus:ring-gorilla-primary/20" />
                        <button @click="copyToClipboard" class="inline-flex items-center justify-center rounded-xl text-sm font-medium bg-gorilla-primary text-white hover:bg-gorilla-primary/90 px-4 py-2">Copy</button>
                    </div>
                </div>
                <div class="p-4 bg-cta-background-one border-t flex justify-end rounded-b-xl">
                    <button @click="showShareModal = false" class="inline-flex items-center justify-center rounded-xl text-sm font-medium bg-white text-gorilla-primary-three hover:bg-gray-50 border border-gray-300 px-4 py-2">Close</button>
                </div>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <div v-if="showCreateEditModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-xl w-full max-w-3xl max-h-[90vh] flex flex-col">
                <div class="p-6 border-b">
                    <h2 class="text-2xl font-bold text-gorilla-primary-three">{{ isEditing ? 'Edit' : 'Create' }} Map</h2>
                </div>

                <div class="p-6 flex-grow overflow-y-auto">
                    <form @submit.prevent="submitPlaceMap" id="placeMapForm">
                        <!-- Create View -->
                        <div v-if="!isEditing" class="space-y-6">
                            <div>
                                <label class="text-sm font-medium leading-none text-gorilla-primary-three" for="name">Map Name</label>
                                <input id="name" v-model="form.name" class="flex h-10 w-full rounded-xl border border-gray-300 bg-white px-3 py-2 text-sm focus:border-gorilla-primary focus:outline-none focus:ring-2 focus:ring-gorilla-primary/20" type="text" required>
                                <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name[0] }}</div>
                            </div>

                            <div>
                                <label class="text-sm font-medium leading-none text-gorilla-primary-three mb-3 block">Type</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <label v-for="mapType in mapTypes" :key="mapType.value"
                                        :class="['flex flex-col items-center justify-center p-4 rounded-xl border-2 cursor-pointer transition-all duration-200', form.type === mapType.value ? 'border-gorilla-primary bg-gorilla-primary/5' : 'border-gray-300 bg-white hover:border-gray-400']">
                                        <input type="radio" :value="mapType.value" v-model="form.type" class="sr-only">
                                        <MapIcon :name="mapType.icon" :size="28" :class="form.type === mapType.value ? 'text-gorilla-primary' : 'text-gray-600'" />
                                        <span :class="['mt-2 text-sm font-medium text-center', form.type === mapType.value ? 'text-gorilla-primary' : 'text-gray-700']">{{ mapType.label }}</span>
                                    </label>
                                </div>
                            </div>

                            <div>
                                <label class="text-sm font-medium leading-none text-gorilla-primary-three mb-3 block">Choose Icon</label>
                                <div class="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-3">
                                    <button v-for="icon in mapIcons" :key="icon.name" type="button"
                                            @click="form.image = icon.name"
                                            :class="['group relative p-3 rounded-xl border-2 transition-all duration-200',
                                                   form.image === icon.name ? 'border-gorilla-primary bg-gorilla-primary/5' : 'border-gray-300 bg-white hover:border-gray-400']">
                                        <div class="flex flex-col items-center space-y-1">
                                            <MapIcon :name="icon.name" :size="24"
                                                   :class="form.image === icon.name ? 'text-gorilla-primary' : 'text-gray-600 group-hover:text-gray-800'" />
                                            <span :class="['text-xs font-medium text-center leading-tight',
                                                         form.image === icon.name ? 'text-gorilla-primary' : 'text-gray-500 group-hover:text-gray-700']">
                                                {{ icon.label }}
                                            </span>
                                        </div>
                                    </button>
                                </div>
                                <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">{{ form.errors.image[0] }}</div>
                            </div>
                        </div>

                        <!-- Edit View -->
                        <div v-else>
                            <div class="border-b border-gray-200 mb-6">
                                <nav class="-mb-px flex space-x-6">
                                    <button type="button" @click="modalTab = 'main'" :class="['py-3 px-1 border-b-2 font-medium text-sm', modalTab === 'main' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300']">
                                        Main Settings
                                    </button>
                                    <button type="button" @click="modalTab = 'customFields'" :class="['py-3 px-1 border-b-2 font-medium text-sm', modalTab === 'customFields' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300']">
                                        Custom Fields
                                    </button>
                                </nav>
                            </div>

                            <div v-show="modalTab === 'main'" class="space-y-5">
                                <div>
                                    <label :class="Label" for="name">Map Name</label>
                                    <input id="name" v-model="form.name" :class="Input" type="text" required>
                                    <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name[0] }}</div>
                                </div>
                                <div>
                                    <label :class="Label" for="description">Description</label>
                                    <textarea id="description" v-model="form.description" :class="Input" rows="3"></textarea>
                                    <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description[0] }}</div>
                                </div>
                                <div>
                                    <label :class="Label" class="mb-3 block">Choose Icon</label>
                                    <div class="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-3">
                                        <button v-for="icon in mapIcons" :key="icon.name" type="button" 
                                                @click="form.image = icon.name"
                                                :class="['group relative p-3 rounded-lg border-2 transition-all duration-200 hover:', 
                                                       form.image === icon.name ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-white hover:border-gray-400']">
                                            <div class="flex flex-col items-center space-y-1">
                                                <MapIcon :name="icon.name" :size="24" 
                                                       :class="form.image === icon.name ? 'text-blue-600' : 'text-gray-600 group-hover:text-gray-800'" />
                                                <span :class="['text-xs font-medium text-center leading-tight', 
                                                             form.image === icon.name ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700']">
                                                    {{ icon.label }}
                                                </span>
                                            </div>
                                        </button>
                                    </div>
                                    <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">{{ form.errors.image[0] }}</div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="p-3 border rounded-lg">
                                        <label :class="Label" class="mb-2 block">Visibility</label>
                                        <div class="flex items-center justify-between">
                                            <span class="flex items-center text-sm text-gray-700">
                                                <LockOpen v-if="form.visibility === 'public'" class="w-5 h-5 mr-2 text-green-600" />
                                                <Lock v-else class="w-5 h-5 mr-2 text-gray-500" />
                                                {{ form.visibility === 'public' ? 'Public' : 'Private' }}
                                            </span>
                                            <button type="button" @click="form.visibility = form.visibility === 'public' ? 'private' : 'public'" role="switch" :aria-checked="form.visibility === 'public'"
                                                    :class="['peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                                                             form.visibility === 'public' ? 'bg-green-500' : 'bg-gray-300']">
                                                <span :class="['pointer-events-none block h-5 w-5 rounded-full bg-white  ring-0 transition-transform',
                                                               form.visibility === 'public' ? 'translate-x-5' : 'translate-x-0']" />
                                            </button>
                                        </div>
                                    </div>
                                    <div class="p-3 border rounded-lg">
                                        <label :class="Label" class="mb-2 block">Status</label>
                                        <div class="flex items-center justify-between">
                                            <span class="flex items-center text-sm text-gray-700">
                                                <CircleCheck v-if="form.status === 'active'" class="w-5 h-5 mr-2 text-green-600" />
                                                <CircleX v-else class="w-5 h-5 mr-2 text-red-600" />
                                                {{ form.status === 'active' ? 'Active' : 'Inactive' }}
                                            </span>
                                            <button type="button" @click="form.status = form.status === 'active' ? 'inactive' : 'active'" role="switch" :aria-checked="form.status === 'active'"
                                                    :class="['peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                                                             form.status === 'active' ? 'bg-green-500' : 'bg-red-500']">
                                                <span :class="['pointer-events-none block h-5 w-5 rounded-full bg-white  ring-0 transition-transform',
                                                               form.status === 'active' ? 'translate-x-5' : 'translate-x-0']" />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-show="modalTab === 'customFields'" class="space-y-4">
                                <div v-for="(field, index) in form.customFields" :key="index" class="grid grid-cols-1 md:grid-cols-5 gap-3 items-center p-3 border rounded-lg bg-gray-50">
                                    <input v-model="field.name" placeholder="Field Name" :class="[Input, 'md:col-span-2']" required>
                                    <select v-model="field.type" :class="Select">
                                        <option value="text">Text</option>
                                        <option value="number">Number</option>
                                        <option value="date">Date</option>
                                    </select>
                                    <select v-model="field.isRequired" :class="Select">
                                        <option value="yes">Required</option>
                                        <option value="no">Optional</option>
                                    </select>
                                    <button type="button" @click="removeCustomField(index)" :class="`${ButtonDestructive} px-2 py-2`">Remove</button>
                                </div>
                                <button type="button" @click="addCustomField" :class="`${ButtonSecondary} px-4 py-2 mt-2`">Add Custom Field</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="p-4 bg-cta-background-one border-t flex justify-end space-x-4 rounded-b-xl">
                    <button type="button" @click="showCreateEditModal = false" class="inline-flex items-center justify-center rounded-xl text-sm font-medium bg-white text-gorilla-primary-three hover:bg-gray-50 border border-gray-300 px-4 py-2">Cancel</button>
                    <button type="submit" form="placeMapForm" :disabled="form.processing" class="inline-flex items-center justify-center rounded-xl text-sm font-medium bg-gorilla-primary text-white hover:bg-gorilla-primary/90 px-4 py-2">
                        {{ form.processing ? 'Saving...' : (isEditing ? 'Update Map' : 'Create Map') }}
                    </button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Using Tailwind classes, so no extra styles needed for now */
</style>