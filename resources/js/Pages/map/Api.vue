<script setup>
import { ref, computed, watch } from "vue";
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Search, Globe, Building, Home, Hospital, Settings, Copy, BookText, BrainCircuit } from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const selectedFilter = ref('all');
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const results = ref({});

// --- Configuration ---
const languages = [
    { code: 'rw', name: 'Kinyarwanda' },
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
];

const filters = [
    { code: 'all', name: 'All Locations', icon: Globe },
    { code: 'province', name: 'Provinces', icon: Building },
    { code: 'district', name: 'Districts', icon: Building },
    { code: 'sector', name: 'Sectors', icon: Home },
    { code: 'cell', name: 'Cells', icon: Home },
    { code: 'village', name: 'Villages', icon: Home },
    { code: 'health_fac', name: 'Health Facilities', icon: Hospital },
    { code: 'pattern', name: 'Search Pattern', icon: BrainCircuit },
];

const searchExamples = [
    'Umudugudu wa Mirambi, Akagari ka Shango',
    'Umudugudu wa Rwakanyambo',
    'Umurenge wa Zaza',
    'Kicukiro District',
    'Intara y\'Iburasirazuba',
];

// --- Computed Properties ---
const hasResults = computed(() => {
  return Object.values(results.value).some(array => array && Array.isArray(array) && array.length > 0);
});

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu mu Rwanda...', 
    en: 'Search Rwanda locations...', 
    fr: 'Rechercher des lieux au Rwanda...', 
}[selectedLanguage.value] || 'Search locations...'));

const curlCommand = computed(() => {
  const data = JSON.stringify({
    searchQuery: searchQuery.value,
    lang: selectedLanguage.value,
    filterData: selectedFilter.value,
  }, null, 2);

  return `curl -X POST "https://onrwanda.com/api/gorilla/search" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '${data}'`;
});


// --- Search Functions ---
const performSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/api/gorilla/search', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });

        results.value = data;

    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        results.value = {};
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) {
        searchQuery.value = '';
    }
    results.value = {};
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
};

const setSearchExample = (example) => {
    searchQuery.value = example;
};

const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text);
  // Maybe show a toast notification here
};

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    performSearch(newQuery, selectedLanguage.value, selectedFilter.value);
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, newLang, selectedFilter.value);
    }
});

watch(selectedFilter, (newFilter) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, selectedLanguage.value, newFilter);
    }
});

</script>

<template>
  <Head title="OnRwanda Geo - API Documentation" />
  <AppLayout>
    <div class="min-h-screen bg-cta-background-two">
      <!-- Header -->
      <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="py-12">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-4xl font-bold text-gorilla-primary-three">
                  API Documentation & Playground
                </h1>
                <p class="mt-3 text-lg text-gray-600">
                  Explore and test the OnRwanda Geo API in real-time.
                </p>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-500 mb-1">API Version</div>
                <div class="text-lg font-bold text-gorilla-primary">v2.1.0</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
          
          <!-- Left Panel: API Controls & Docs -->
          <div class="lg:col-span-1 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary">API Controls</CardTitle>
                <CardDescription>
                  Adjust parameters to test the API.
                </CardDescription>
              </CardHeader>
              <CardContent class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gorilla-primary-three mb-2">Language</label>
                  <Select v-model="selectedLanguage">
                    <SelectTrigger class="h-10 border border-gray-300 rounded-lg focus:border-gorilla-primary">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="lang in languages" :key="lang.code" :value="lang.code">
                        {{ lang.name }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gorilla-primary-three mb-2">Filter</label>
                  <Select v-model="selectedFilter">
                    <SelectTrigger class="h-10 border border-gray-300 rounded-lg focus:border-gorilla-primary">
                      <SelectValue placeholder="Select filter" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="filter in filters" :key="filter.code" :value="filter.code">
                        <div class="flex items-center gap-2">
                          <component :is="filter.icon" class="w-4 h-4" />
                          {{ filter.name }}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary flex items-center">
                  <BookText class="mr-2 h-5 w-5" />
                  How to Search
                </CardTitle>
                <CardDescription>
                  Use these examples to get started.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul class="space-y-2">
                  <li v-for="example in searchExamples" :key="example">
                    <button @click="setSearchExample(example)" class="text-left w-full text-sm text-gray-600 hover:text-gorilla-primary-three p-2 rounded-md hover:bg-cta-background-one transition-colors">
                      {{ example }}
                    </button>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <!-- Right Panel: Playground -->
          <div class="lg:col-span-2">
            <Card class="overflow-hidden">
              <CardHeader>
                <CardTitle class="text-gorilla-primary">Playground</CardTitle>
                <CardDescription>
                  Enter a query to see the live API response.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div class="grid grid-cols-1 gap-8">
                  <!-- Input Column -->
                  <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gorilla-primary-three">Request</h3>
                    <div class="relative">
                      <Input
                        v-model="searchQuery"
                        :placeholder="getPlaceholderText"
                        class="h-12 pl-12 pr-4 text-base border border-gray-300 rounded-lg focus:border-gorilla-primary focus:ring-0 transition-colors"
                      />
                      <Search class="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                    </div>
                    <div class="bg-gray-900 rounded-lg mt-4">
                      <div class="px-4 py-2 border-b border-gray-700 flex items-center justify-between">
                        <span class="text-xs font-mono text-gray-400">cURL Command</span>
                        <Button variant="ghost" size="sm" @click="copyToClipboard(curlCommand)" class="text-gray-400 hover:text-white">
                          <Copy class="h-3 w-3 mr-2" />
                          Copy
                        </Button>
                      </div>
                      <div class="p-4 text-xs text-white font-mono overflow-x-auto">
                        <pre>{{ curlCommand }}</pre>
                      </div>
                    </div>
                  </div>

                  <!-- Output Column -->
                  <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gorilla-primary-three">Response</h3>
                    <div class="relative bg-gray-900 rounded-lg border border-gray-200 h-[450px] flex flex-col">
                      <div class="px-4 py-2 border-b border-gray-700 flex items-center justify-between flex-shrink-0">
                        <div class="flex items-center space-x-2">
                          <div :class="['w-2 h-2 rounded-full', isLoading ? 'bg-yellow-400 animate-pulse' : hasResults ? 'bg-green-400' : 'bg-gray-500']"></div>
                          <span class="text-xs font-mono text-gray-400">
                            {{ isLoading ? 'Loading...' : `Status: ${error ? 'Error' : 'OK'}` }}
                          </span>
                        </div>
                        <span v-if="!isLoading && searchTime > 0" class="text-xs font-mono text-gray-500">
                          {{ searchTime }}ms
                        </span>
                      </div>
                      <div class="p-4 text-xs text-white font-mono overflow-y-auto flex-grow">
                        <div v-if="isLoading" class="flex items-center justify-center h-full">
                          <div class="animate-spin rounded-full h-6 w-6 border-2 border-gray-400 border-t-transparent"></div>
                        </div>
                        <div v-else-if="error" class="text-red-400">
                          <pre>{{ JSON.stringify({ error: error }, null, 2) }}</pre>
                        </div>
                        <div v-else-if="hasResults">
                          <pre>{{ JSON.stringify(results, null, 2) }}</pre>
                        </div>
                        <div v-else class="text-gray-500 h-full flex items-center justify-center">
                          <p>Enter a query to see results</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<style scoped>
.font-mono {
    font-family: 'Roboto Mono', monospace;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
