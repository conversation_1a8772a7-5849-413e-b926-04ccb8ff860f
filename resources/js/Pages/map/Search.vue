<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
    Search,
    Globe,
    Building,
    MapPin,
    X,
    Layers,
    Navigation,
    Crosshair,
    Loader2,
    AlertCircle,
    Clock,
    LocateFixed
} from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';
import AppLayout from '@/Layouts/AppLayout.vue';

// --- REACTIVE STATE ---
const searchQuery = ref('');
const searchMode = ref('text'); // 'text' or 'coordinate'
const selectedLanguage = ref('en');
const selectedFilter = ref('all');
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const searchResults = ref({});
const coordinateForm = ref({
    latitude: '',
    longitude: ''
});

// Map related state
const mapContainer = ref(null);
const map = ref(null);
const currentMarker = ref(null);
const currentGeoJsonLayer = ref(null);
const mapTheme = ref('default');

// Rwanda bounds for map constraints
const RWANDA_BOUNDS = [
    [28.8617546, -2.9389804], // Southwest coordinates
    [30.8997343, -1.0474083]  // Northeast coordinates
];

// --- COMPUTED PROPERTIES ---
const canSearch = computed(() => {
    if (searchMode.value === 'text') {
        return searchQuery.value.trim().length >= 3;
    } else {
        const lat = parseFloat(coordinateForm.value.latitude);
        const lng = parseFloat(coordinateForm.value.longitude);
        return !isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
    }
});

const hasResults = computed(() => {
    return Object.values(searchResults.value).some(array =>
        array && Array.isArray(array) && array.length > 0
    );
});

const totalResults = computed(() => {
    return Object.values(searchResults.value).reduce((sum, array) =>
        sum + (Array.isArray(array) ? array.length : 0), 0
    );
});

const getPlaceholderText = computed(() => {
    const placeholders = {
        text: {
            rw: 'Shakisha ahantu mu Rwanda...',
            en: 'Search Rwanda locations...',
            fr: 'Rechercher des lieux au Rwanda...'
        },
        coordinate: {
            rw: 'Injiza latitude,longitude...',
            en: 'Enter latitude,longitude...',
            fr: 'Entrez latitude,longitude...'
        }
    };
    return placeholders[searchMode.value][selectedLanguage.value] || 'Search...';
});

// Language and filter options
const languages = [
    { code: 'rw', name: 'Kinyarwanda', flag: '🇷🇼' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' }
];

const filters = [
    { value: 'all', label: 'All Locations' },
    { value: 'province', label: 'Provinces' },
    { value: 'district', label: 'Districts' },
    { value: 'sector', label: 'Sectors' },
    { value: 'cell', label: 'Cells' },
    { value: 'village', label: 'Villages' }
];

const mapThemes = [
    { value: 'default', label: 'Default' },
    { value: 'satellite', label: 'Satellite' }
];

// --- METHODS ---
const initializeMap = () => {
    if (!mapContainer.value) return;

    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: getMapStyle(),
        center: [29.8739, -1.9403], // Rwanda center
        zoom: 8,
        maxBounds: RWANDA_BOUNDS,
        minZoom: 6,
        maxZoom: 18,
        attributionControl: false,
        maxBoundsViscosity: 1.0
    });

    // Add navigation controls
    map.value.addControl(new maplibregl.NavigationControl(), 'top-right');
    map.value.addControl(new maplibregl.AttributionControl({ compact: true }), 'bottom-right');

    // Add click handler for coordinate search
    map.value.on('click', handleMapClick);

    // Add load handler
    map.value.on('load', () => {
        console.log('Map loaded successfully');
    });
};

const getMapStyle = () => {
    const themes = {
        default: {
            url: 'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            paint: { 'raster-saturation': -0.2, 'raster-contrast': 0.1, 'raster-opacity': 0.9 }
        },
        satellite: {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles © Esri',
            paint: {}
        }
    };

    return {
        version: 8,
        sources: {
            osm: {
                type: 'raster',
                tiles: [themes[mapTheme.value]?.url || themes.default.url],
                tileSize: 256,
                attribution: themes[mapTheme.value]?.attribution || themes.default.attribution
            }
        },
        layers: [{
            id: 'osm-tiles',
            type: 'raster',
            source: 'osm',
            paint: themes[mapTheme.value]?.paint || themes.default.paint
        }],
    };
};

const handleMapClick = (e) => {
    if (searchMode.value !== 'coordinate') return;

    const { lng, lat } = e.lngLat;

    // Check if coordinates are within Rwanda bounds
    if (!isWithinRwandaBounds(lat, lng)) {
        error.value = 'Please click within Rwanda boundaries';
        setTimeout(() => error.value = null, 3000);
        return;
    }

    coordinateForm.value.latitude = lat.toFixed(6);
    coordinateForm.value.longitude = lng.toFixed(6);

    // Add marker at clicked location
    addMarker(lat, lng);

    // Trigger search
    performSearch();
};

const isWithinRwandaBounds = (lat, lng) => {
    const [swLng, swLat] = RWANDA_BOUNDS[0];
    const [neLng, neLat] = RWANDA_BOUNDS[1];
    return lat >= swLat && lat <= neLat && lng >= swLng && lng <= neLng;
};

const addMarker = (lat, lng, popup = null) => {
    // Remove existing marker
    if (currentMarker.value) {
        currentMarker.value.remove();
    }

    // Create new marker
    currentMarker.value = new maplibregl.Marker({
        color: '#1A773E' // gorilla-primary color
    })
    .setLngLat([lng, lat])
    .addTo(map.value);

    if (popup) {
        currentMarker.value.setPopup(new maplibregl.Popup().setHTML(popup));
    }
};

const clearMapLayers = () => {
    // Remove existing GeoJSON layer
    if (currentGeoJsonLayer.value && map.value.getLayer(currentGeoJsonLayer.value)) {
        map.value.removeLayer(currentGeoJsonLayer.value);
        map.value.removeSource(currentGeoJsonLayer.value);
        currentGeoJsonLayer.value = null;
    }

    // Remove existing marker
    if (currentMarker.value) {
        currentMarker.value.remove();
        currentMarker.value = null;
    }
};

const renderGeoJSON = (geojson, type) => {
    if (!map.value || !geojson) return;

    clearMapLayers();

    try {
        const geoJsonData = typeof geojson === 'string' ? JSON.parse(geojson) : geojson;
        const layerId = `geojson-layer-${Date.now()}`;

        // Add source
        map.value.addSource(layerId, {
            type: 'geojson',
            data: geoJsonData
        });

        // Get style based on type
        const style = getGeoJSONStyle(type);

        // Add fill layer
        map.value.addLayer({
            id: layerId,
            type: 'fill',
            source: layerId,
            paint: style.fill
        });

        // Add outline layer
        map.value.addLayer({
            id: `${layerId}-outline`,
            type: 'line',
            source: layerId,
            paint: style.outline
        });

        currentGeoJsonLayer.value = layerId;

        // Fit map to bounds
        const bounds = new maplibregl.LngLatBounds();
        const addCoordinates = (coords) => {
            if (Array.isArray(coords[0])) {
                coords.forEach(addCoordinates);
            } else {
                bounds.extend(coords);
            }
        };

        if (geoJsonData.geometry) {
            addCoordinates(geoJsonData.geometry.coordinates);
        }

        map.value.fitBounds(bounds, { padding: 50 });

    } catch (error) {
        console.error('Error rendering GeoJSON:', error);
    }
};

const getGeoJSONStyle = (type) => {
    const styles = {
        provinces: {
            fill: { 'fill-color': '#1A773E', 'fill-opacity': 0.3 },
            outline: { 'line-color': '#1A773E', 'line-width': 2 }
        },
        districts: {
            fill: { 'fill-color': '#1C5172', 'fill-opacity': 0.3 },
            outline: { 'line-color': '#1C5172', 'line-width': 2 }
        },
        sectors: {
            fill: { 'fill-color': '#303017', 'fill-opacity': 0.3 },
            outline: { 'line-color': '#303017', 'line-width': 2 }
        },
        cells: {
            fill: { 'fill-color': '#1A773E', 'fill-opacity': 0.2 },
            outline: { 'line-color': '#1A773E', 'line-width': 1 }
        },
        villages: {
            fill: { 'fill-color': '#1C5172', 'fill-opacity': 0.2 },
            outline: { 'line-color': '#1C5172', 'line-width': 1 }
        }
    };

    return styles[type] || styles.districts;
};

// Search functionality
const performSearch = debounce(async () => {
    if (!canSearch.value) {
        clearSearch();
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    let query = searchMode.value === 'text'
        ? searchQuery.value.trim()
        : `${coordinateForm.value.latitude},${coordinateForm.value.longitude}`;

    try {
        const { data } = await axios.post('/gorilla/search', {
            searchQuery: query,
            lang: selectedLanguage.value,
            filterData: selectedFilter.value,
        });

        // Initialize results structure
        const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'others'];
        searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));

        if (searchMode.value === 'coordinate') {
            // For coordinate search, handle single result object
            if (data && typeof data === 'object' && !Array.isArray(data)) {
                // Single result from coordinate search
                const processedResult = {
                    ...data,
                    type: data.type || 'other',
                    geojson: typeof data.geojson === 'string' ? JSON.parse(data.geojson) : data.geojson,
                    latitude: typeof data.latitude === 'string' ? parseFloat(data.latitude) : data.latitude,
                    longitude: typeof data.longitude === 'string' ? parseFloat(data.longitude) : data.longitude,
                };

                // Place result in appropriate category based on type
                const resultType = data.type || 'others';
                const categoryKey = resultType.endsWith('s') ? resultType : `${resultType}s`;
                if (searchResults.value[categoryKey]) {
                    searchResults.value[categoryKey] = [processedResult];
                } else {
                    searchResults.value.others = [processedResult];
                }
            }
        } else {
            // For text search, handle grouped results
            for (const type of ALL_RESULT_TYPES) {
                if (data[type] && Array.isArray(data[type])) {
                    searchResults.value[type] = data[type].map(item => ({
                        ...item,
                        type: item.type || type.slice(0, -1), // Remove 's' from plural
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }));
                }
            }
        }

        searchTime.value = Math.round(performance.now() - startTime);

        // Handle coordinate search results
        if (searchMode.value === 'coordinate') {
            handleCoordinateSearchResults();
        }

    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Search failed. Please try again.';
        searchResults.value = {};
    } finally {
        isLoading.value = false;
    }
}, 300);

const handleCoordinateSearchResults = () => {
    // Find any result from coordinate search across all categories
    let foundResult = null;
    const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'others'];

    for (const type of ALL_RESULT_TYPES) {
        if (searchResults.value[type] && searchResults.value[type].length > 0) {
            foundResult = searchResults.value[type][0];
            break;
        }
    }

    if (foundResult) {
        // If result has geojson, render it
        if (foundResult.geojson) {
            renderGeoJSON(foundResult.geojson, foundResult.type || 'other');
        } else if (foundResult.latitude && foundResult.longitude) {
            // Fallback to marker
            const lat = parseFloat(foundResult.latitude);
            const lng = parseFloat(foundResult.longitude);
            addMarker(lat, lng, `<strong>${foundResult.name || 'Location'}</strong>`);
            map.value.flyTo({ center: [lng, lat], zoom: 14 });
        }
    } else if (canSearch.value) {
        // No results found, just show marker at searched coordinates
        const lat = parseFloat(coordinateForm.value.latitude);
        const lng = parseFloat(coordinateForm.value.longitude);
        addMarker(lat, lng, 'No location data found');
        map.value.flyTo({ center: [lng, lat], zoom: 14 });
    }
};



const parseCoordinateInput = (input) => {
    const coordPattern = /^(-?\d+\.?\d*),\s*(-?\d+\.?\d*)$/;
    const match = input.match(coordPattern);

    if (match) {
        const lat = parseFloat(match[1]);
        const lng = parseFloat(match[2]);

        // Validate coordinate ranges
        if (lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
            return { latitude: lat, longitude: lng };
        }
    }
    return null;
};

const handleSearchModeChange = () => {
    clearSearch();
    if (searchMode.value === 'coordinate') {
        searchQuery.value = '';
    } else {
        coordinateForm.value = { latitude: '', longitude: '' };
    }
};

const handleTextInput = (value) => {
    searchQuery.value = value;
    performSearch();
};

const handleCoordinateInput = (value) => {
    searchQuery.value = value;

    // Try to parse coordinate input like "-1.9441,30.0619"
    const coords = parseCoordinateInput(value);
    if (coords) {
        coordinateForm.value.latitude = coords.latitude.toString();
        coordinateForm.value.longitude = coords.longitude.toString();
        performSearch();
    }
};

const updateCoordinateSearch = () => {
    // Update the main search query when coordinates are entered manually
    if (coordinateForm.value.latitude && coordinateForm.value.longitude) {
        searchQuery.value = `${coordinateForm.value.latitude},${coordinateForm.value.longitude}`;
        performSearch();
    }
};

const clearSearch = () => {
    searchQuery.value = '';
    coordinateForm.value = { latitude: '', longitude: '' };
    searchResults.value = {};
    error.value = null;
    searchTime.value = 0;
    clearMapLayers();
};

const handleResultClick = (result, type) => {
    // Clear previous selections
    clearMapLayers();

    if (result.geojson) {
        // Render GeoJSON and zoom to bounds
        renderGeoJSON(result.geojson, result.type || type);
    } else if (result.latitude && result.longitude) {
        // Use lat/long to zoom and place marker
        const lat = parseFloat(result.latitude);
        const lng = parseFloat(result.longitude);

        // Create popup content
        const popupContent = `
            <div class="p-2">
                <h3 class="font-semibold text-gorilla-primary-three">${result.name}</h3>
                <p class="text-sm text-gray-600">${result.address || ''}</p>
                <p class="text-xs text-gray-500 mt-1">
                    ${lat.toFixed(6)}, ${lng.toFixed(6)}
                </p>
            </div>
        `;

        addMarker(lat, lng, popupContent);
        map.value.flyTo({
            center: [lng, lat],
            zoom: 14,
            duration: 1000
        });
    }

    // Store selected result key for highlighting
    if (result.key) {
        console.log('Selected result key:', result.key);
    }
};

const getDisplayName = (result) => {
    const langKey = `name_${selectedLanguage.value}`;
    return result[langKey] || result.name_en || result.name_local || result.name || 'N/A';
};

const getDisplayAddress = (result) => {
    // Try different address fields based on the result format
    return result.address ||
           result[`full_address_${selectedLanguage.value}`] ||
           result.full_address_en ||
           result.full_address_fr ||
           result.full_address_rw ||
           '';
};

const getUserLocation = () => {
    if (!navigator.geolocation) {
        error.value = "Geolocation is not supported by your browser.";
        return;
    }

    isLoading.value = true;
    navigator.geolocation.getCurrentPosition(
        (position) => {
            coordinateForm.value.latitude = position.coords.latitude.toFixed(6);
            coordinateForm.value.longitude = position.coords.longitude.toFixed(6);

            // Add marker at user location
            addMarker(position.coords.latitude, position.coords.longitude, 'Your Location');

            // Switch to coordinate mode and trigger search
            searchMode.value = 'coordinate';
            performSearch();
        },
        (err) => {
            isLoading.value = false;
            error.value = `Failed to get location: ${err.message}`;
        }
    );
};

// --- LIFECYCLE HOOKS ---
onMounted(() => {
    nextTick(() => {
        initializeMap();
    });
});

onUnmounted(() => {
    if (map.value) {
        map.value.remove();
    }
});

// --- WATCHERS ---
watch(mapTheme, () => {
    if (map.value && map.value.isStyleLoaded()) {
        const newStyle = getMapStyle();
        map.value.setStyle(newStyle);
    }
});

watch(searchMode, () => {
    handleSearchModeChange();
});

watch([() => coordinateForm.value.latitude, () => coordinateForm.value.longitude], () => {
    if (searchMode.value === 'coordinate' && canSearch.value) {
        // Update the main search query when coordinates change
        searchQuery.value = `${coordinateForm.value.latitude},${coordinateForm.value.longitude}`;
    }
}, { deep: true });
</script>

<template>
    <AppLayout title="Rwanda Geo Search">
        <div class="min-h-screen bg-cta-background-two">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-6 lg:px-8">
                    <div class="py-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-gorilla-primary-three">
                                    Rwanda Geo Search
                                </h1>
                                <p class="mt-2 text-gray-600">
                                    Search locations by name or coordinates with interactive map visualization
                                </p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <!-- Map Theme Selector -->
                                <Select v-model="mapTheme">
                                    <SelectTrigger class="w-32 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20">
                                        <Layers class="mr-2 h-4 w-4" />
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem
                                            v-for="theme in mapThemes"
                                            :key="theme.value"
                                            :value="theme.value"
                                        >
                                            {{ theme.label }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="max-w-7xl mx-auto px-6 lg:px-8 py-8">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Search Panel -->
                    <div class="lg:col-span-1 space-y-6">
                        <!-- Search Mode Toggle -->
                        <Card>
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary flex items-center">
                                    <Search class="mr-2 h-5 w-5" />
                                    Search Mode
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center space-x-2">
                                        <Switch
                                            :checked="searchMode === 'coordinate'"
                                            @update:checked="searchMode = $event ? 'coordinate' : 'text'"
                                        />
                                        <Label class="text-sm font-medium">
                                            {{ searchMode === 'coordinate' ? 'Coordinate Mode' : 'Text Mode' }}
                                        </Label>
                                    </div>
                                    <div class="flex items-center text-xs text-gray-500">
                                        <Crosshair v-if="searchMode === 'coordinate'" class="mr-1 h-3 w-3" />
                                        <Globe v-else class="mr-1 h-3 w-3" />
                                        {{ searchMode === 'coordinate' ? 'Click map or enter lat,lng' : 'Search by location name' }}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <!-- Search Input -->
                        <Card>
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary">Search Input</CardTitle>
                            </CardHeader>
                            <CardContent class="space-y-4">
                                <!-- Text Search Input -->
                                <div v-if="searchMode === 'text'" class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search class="h-5 w-5 text-gray-400" />
                                    </div>
                                    <Input
                                        v-model="searchQuery"
                                        @input="handleTextInput($event.target.value)"
                                        :placeholder="getPlaceholderText"
                                        class="pl-10 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                                    />
                                    <div v-if="isLoading" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <Loader2 class="h-5 w-5 text-gorilla-primary animate-spin" />
                                    </div>
                                </div>

                                <!-- Coordinate Search Input -->
                                <div v-else class="space-y-3">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <Navigation class="h-5 w-5 text-gray-400" />
                                        </div>
                                        <Input
                                            v-model="searchQuery"
                                            @input="handleCoordinateInput($event.target.value)"
                                            :placeholder="getPlaceholderText"
                                            class="pl-10 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                                        />
                                        <div v-if="isLoading" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                            <Loader2 class="h-5 w-5 text-gorilla-primary animate-spin" />
                                        </div>
                                    </div>
                                    <div class="text-center text-sm text-gray-500">or enter coordinates separately below</div>
                                </div>

                                <!-- Coordinate Form (when in coordinate mode) -->
                                <div v-if="searchMode === 'coordinate'" class="grid grid-cols-2 gap-3">
                                    <div>
                                        <Label class="text-sm font-medium text-gray-700">Latitude</Label>
                                        <Input
                                            v-model="coordinateForm.latitude"
                                            @input="updateCoordinateSearch"
                                            type="number"
                                            step="any"
                                            placeholder="-1.9441"
                                            class="mt-1 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                                        />
                                    </div>
                                    <div>
                                        <Label class="text-sm font-medium text-gray-700">Longitude</Label>
                                        <Input
                                            v-model="coordinateForm.longitude"
                                            @input="updateCoordinateSearch"
                                            type="number"
                                            step="any"
                                            placeholder="30.0619"
                                            class="mt-1 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                                        />
                                    </div>
                                </div>

                                <!-- Language and Filter Selection -->
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <Label class="text-sm font-medium text-gray-700">Language</Label>
                                        <Select v-model="selectedLanguage">
                                            <SelectTrigger class="mt-1 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem
                                                    v-for="lang in languages"
                                                    :key="lang.code"
                                                    :value="lang.code"
                                                >
                                                    {{ lang.flag }} {{ lang.name }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label class="text-sm font-medium text-gray-700">Filter</Label>
                                        <Select v-model="selectedFilter">
                                            <SelectTrigger class="mt-1 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem
                                                    v-for="filter in filters"
                                                    :key="filter.value"
                                                    :value="filter.value"
                                                >
                                                    {{ filter.label }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="grid grid-cols-2 gap-3">
                                    <Button
                                        @click="clearSearch"
                                        variant="outline"
                                        class="border-gray-300 text-gray-700 hover:bg-gray-50"
                                    >
                                        <X class="mr-2 h-4 w-4" />
                                        Clear
                                    </Button>
                                    <Button
                                        v-if="searchMode === 'coordinate'"
                                        @click="getUserLocation"
                                        variant="outline"
                                        class="border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white"
                                        :disabled="isLoading"
                                    >
                                        <LocateFixed class="mr-2 h-4 w-4" />
                                        My Location
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>

                        <!-- Search Results -->
                        <Card v-if="hasResults || isLoading || error">
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary flex items-center justify-between">
                                    <div class="flex items-center">
                                        <Building class="mr-2 h-5 w-5" />
                                        Search Results
                                    </div>
                                    <div class="flex items-center space-x-2 text-sm">
                                        <span v-if="isLoading" class="text-gray-500 flex items-center">
                                            <Loader2 class="mr-1 h-3 w-3 animate-spin" />
                                            Searching...
                                        </span>
                                        <span v-else-if="error" class="text-red-600 flex items-center">
                                            <AlertCircle class="mr-1 h-3 w-3" />
                                            Error
                                        </span>
                                        <span v-else class="text-gray-600">
                                            {{ totalResults }} result{{ totalResults !== 1 ? 's' : '' }}
                                        </span>
                                        <span v-if="searchTime > 0 && !isLoading" class="text-xs bg-gorilla-primary/10 text-gorilla-primary px-2 py-1 rounded-full flex items-center">
                                            <Clock class="mr-1 h-3 w-3" />
                                            {{ searchTime }}ms
                                        </span>
                                    </div>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <!-- Error Message -->
                                <div v-if="error" class="p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="flex items-center">
                                        <AlertCircle class="h-5 w-5 text-red-600 mr-2" />
                                        <span class="text-red-800">{{ error }}</span>
                                    </div>
                                </div>

                                <!-- Loading State -->
                                <div v-else-if="isLoading" class="p-8 text-center">
                                    <Loader2 class="h-8 w-8 text-gorilla-primary animate-spin mx-auto mb-2" />
                                    <p class="text-gray-600">Searching Rwanda locations...</p>
                                </div>

                                <!-- Results List -->
                                <div v-else-if="hasResults" class="max-h-96 overflow-y-auto space-y-2">
                                    <template v-for="(resultArray, type) in searchResults" :key="type">
                                        <div v-if="resultArray && resultArray.length > 0">
                                            <!-- Type Header -->
                                            <div class="sticky top-0 bg-cta-background-one px-3 py-2 rounded-lg mb-2">
                                                <h4 class="text-sm font-semibold text-gorilla-primary-three capitalize">
                                                    {{ type }} ({{ resultArray.length }})
                                                </h4>
                                            </div>

                                            <!-- Results -->
                                            <div class="space-y-1">
                                                <div
                                                    v-for="result in resultArray"
                                                    :key="`${type}-${result.key || result.id}`"
                                                    @click="handleResultClick(result, type)"
                                                    class="p-3 rounded-lg border border-gray-200 hover:border-gorilla-primary hover:bg-gorilla-primary/5 cursor-pointer transition-all duration-200"
                                                >
                                                    <div class="flex items-start justify-between">
                                                        <div class="flex-1 min-w-0">
                                                            <h5 class="font-medium text-gorilla-primary-three truncate">
                                                                {{ getDisplayName(result) }}
                                                            </h5>
                                                            <p v-if="getDisplayAddress(result)" class="text-sm text-gray-600 truncate mt-1">
                                                                {{ getDisplayAddress(result) }}
                                                            </p>
                                                            <div class="flex items-center mt-2 space-x-2">
                                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gorilla-primary/10 text-gorilla-primary">
                                                                    {{ result.type || type.slice(0, -1) }}
                                                                </span>
                                                                <span v-if="result.latitude && result.longitude" class="text-xs text-gray-500">
                                                                    {{ parseFloat(result.latitude).toFixed(4) }}, {{ parseFloat(result.longitude).toFixed(4) }}
                                                                </span>
                                                                <span v-if="result.key" class="text-xs text-gray-400 font-mono">
                                                                    {{ result.key.substring(0, 8) }}...
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="ml-3 flex-shrink-0">
                                                            <MapPin class="h-5 w-5 text-gorilla-primary" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>

                                <!-- No Results -->
                                <div v-else class="p-8 text-center">
                                    <Search class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <p class="text-gray-600">No locations found</p>
                                    <p class="text-sm text-gray-500 mt-1">Try adjusting your search terms or filters</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <!-- Map Panel -->
                    <div class="lg:col-span-2">
                        <Card class="h-full">
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary flex items-center justify-between">
                                    <div class="flex items-center">
                                        <Globe class="mr-2 h-5 w-5" />
                                        Interactive Map
                                    </div>
                                    <div v-if="searchMode === 'coordinate'" class="text-sm text-gray-600 flex items-center">
                                        <Crosshair class="mr-1 h-4 w-4" />
                                        Click to search
                                    </div>
                                </CardTitle>
                            </CardHeader>
                            <CardContent class="p-0">
                                <div
                                    ref="mapContainer"
                                    class="w-full h-96 lg:h-[600px] rounded-b-lg"
                                    style="min-height: 400px;"
                                ></div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* MapLibre GL CSS is imported globally */

/* Custom scrollbar for results */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Smooth transitions */
.transition-all {
    transition: all 0.2s ease-in-out;
}

/* Map container styling */
.maplibregl-map {
    border-radius: 0 0 0.5rem 0.5rem;
}

/* Custom marker styling */
.maplibregl-marker {
    cursor: pointer;
}

/* Loading animation */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Result item hover effects */
.result-item {
    transition: all 0.2s ease-in-out;
}

.result-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(26, 119, 62, 0.1);
}

/* Map theme transition */
.maplibregl-canvas {
    transition: opacity 0.3s ease-in-out;
}

/* Custom focus styles for accessibility */
.focus\:ring-gorilla-primary\/20:focus {
    --tw-ring-color: rgba(26, 119, 62, 0.2);
}

/* Sticky header in results */
.sticky {
    position: sticky;
    z-index: 10;
}

/* Custom badge styling */
.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
}

/* Map controls positioning */
.maplibregl-ctrl-top-right {
    top: 10px;
    right: 10px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .maplibregl-map {
        height: 400px !important;
    }
}

@media (max-width: 768px) {
    .maplibregl-map {
        height: 300px !important;
    }
}
</style>
