<script setup>
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { <PERSON>, <PERSON> } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Search, MapPin, Globe, Building, Home, Hospital, Settings, Menu, X, ChevronDown, GraduationCap, Store, ChevronLeft, ChevronRight } from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const selectedFilter = ref('all');
const showFilters = ref(false);
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const mobileMenuOpen = ref(false);
const scrollContainer = ref(null);
const canScrollLeft = ref(false);
const canScrollRight = ref(false);

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'others'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);

// Configuration
const languages = [
    { code: 'rw', name: 'Kinyarwanda' },
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
];

const filters = [
    { code: 'all', name: 'All Locations', icon: Globe },
    { code: 'province', name: 'Provinces', icon: Building },
    { code: 'district', name: 'Districts', icon: Building },
    { code: 'sector', name: 'Sectors', icon: Home },
    { code: 'cell', name: 'Cells', icon: Home },
    { code: 'village', name: 'Villages', icon: Home },
];

const searchExamples = [
    'Umudugudu wa Mirambi, Akagari ka Shango',
    'Umudugudu wa Rwakanyambo',
    'Umurenge wa Zaza',
    'Umurenge wa Mushubati',
    'Akagari ka Nyagisozi',
    'Akarere ka Nyamagabe',
    'Kicukiro District',
    'KK 8 Avenue',
];

// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu mu Rwanda...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux au Rwanda...',
}[selectedLanguage.value] || 'Search locations...'));

// --- Scroll Functions ---
const checkScroll = () => {
    if (!scrollContainer.value) return;
    const el = scrollContainer.value;
    canScrollLeft.value = el.scrollLeft > 0;
    canScrollRight.value = el.scrollLeft < (el.scrollWidth - el.clientWidth - 10);
};

const scrollLeft = () => {
    if (scrollContainer.value) {
        scrollContainer.value.scrollBy({ left: -300, behavior: 'smooth' });
    }
};

const scrollRight = () => {
    if (scrollContainer.value) {
        scrollContainer.value.scrollBy({ left: 300, behavior: 'smooth' });
    }
};

// --- Search Functions ---
const performSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/api/gorilla/search', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });

        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) {
        searchQuery.value = '';
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
};

const setSearchExample = (example) => {
    searchQuery.value = example;
};

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    performSearch(newQuery, selectedLanguage.value, selectedFilter.value);
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, newLang, selectedFilter.value);
    }
});

watch(selectedFilter, (newFilter) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, selectedLanguage.value, newFilter);
    }
});

// --- Lifecycle ---
onMounted(() => {
    nextTick(() => {
        checkScroll();
        if (scrollContainer.value) {
            scrollContainer.value.addEventListener('scroll', checkScroll);
        }
    });
});
</script>

<template>
    <Head title="Rwanda Geo - Professional Geocoding Platform" />
    <AppLayout>
        <div class="min-h-screen bg-cta-background-two">

            <!-- Hero Section with Fixed Height Heading -->
            <section class="px-4 sm:px-6 lg:px-8">
                <div class="max-w-6xl mx-auto">
                    <!-- Fixed Height Container for Heading -->
                    <div class="flex flex-col items-center justify-center" 
                         :class="hasResults ? 'h-[25vh] py-8' : 'h-[50vh] py-20'"
                         style="transition: height 0.5s ease-in-out;">
                        <!-- Main Heading -->
                        <div class="text-center">
                            <h1 class="text-4xl md:text-5xl lg:text-6xl font-normal text-gorilla-primary-three tracking-tight mb-4">
                                TKD Maps Platform
                            </h1>
                            <p class="text-lg md:text-xl text-gray-600 font-light max-w-2xl mx-auto">
                                Professional geocoding platform for Rwanda's administrative boundaries and locations
                            </p>
                        </div>
                    </div>

                    <!-- Search Section -->
                    <div class="pb-20">
                        <div class="flex flex-col items-center space-y-8">

                        <!-- Search Card -->
                        <div class="w-full max-w-2xl">
                            <Card class="border border-gray-200 rounded-2xl bg-white">
                                <CardHeader class="pb-4">
                                    <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                                        <CardTitle class="text-lg font-medium text-gorilla-primary-three">Search Locations</CardTitle>
                                        <button
                                            @click="showFilters = !showFilters"
                                            class="border border-gorilla-primary-two text-gorilla-primary-two hover:bg-gorilla-primary-two hover:text-white rounded-lg px-3 py-2 text-sm font-medium flex items-center gap-2 transition-colors"
                                        >
                                            <Settings class="w-4 h-4" />
                                            Filters
                                        </button>
                                    </div>
                                </CardHeader>
                                <CardContent class="space-y-4">
                                    <!-- Search Input -->
                                    <div class="relative">
                                        <Input
                                            v-model="searchQuery"
                                            :placeholder="getPlaceholderText"
                                            class="h-12 pl-12 pr-4 text-base border border-gray-300 rounded-lg focus:border-gorilla-primary focus:ring-0 transition-colors"
                                        />
                                        <Search class="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                    </div>

                                    <!-- Filters Panel -->
                                    <div v-if="showFilters" class="space-y-4 p-4 bg-cta-background-one rounded-lg border border-gray-200">
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <!-- Language Filter -->
                                            <div>
                                                <label class="block text-sm font-medium text-gorilla-primary-three mb-2">Language</label>
                                                <Select v-model="selectedLanguage">
                                                    <SelectTrigger class="h-10 border border-gray-300 rounded-lg focus:border-gorilla-primary">
                                                        <SelectValue placeholder="Select language" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem v-for="lang in languages" :key="lang.code" :value="lang.code">
                                                            {{ lang.name }}
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <!-- Location Type Filter -->
                                            <div>
                                                <label class="block text-sm font-medium text-gorilla-primary-three mb-2">Location Type</label>
                                                <Select v-model="selectedFilter">
                                                    <SelectTrigger class="h-10 border border-gray-300 rounded-lg focus:border-gorilla-primary">
                                                        <SelectValue placeholder="Select type" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem v-for="filter in filters" :key="filter.code" :value="filter.code">
                                                            <div class="flex items-center gap-2">
                                                                <component :is="filter.icon" class="w-4 h-4" />
                                                                {{ filter.name }}
                                                            </div>
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <!-- Search Results Container with 700px max height -->
                            <div v-if="isLoading || hasResults || error" class="mt-4">
                                <Card class="border border-gray-200 rounded-2xl bg-white overflow-hidden">
                                    <CardContent class="p-0">
                                        <!-- Loading State -->
                                        <div v-if="isLoading" class="flex items-center justify-center py-8">
                                            <div class="flex items-center space-x-2">
                                                <div class="animate-spin rounded-full h-5 w-5 border-2 border-gray-400 border-t-transparent"></div>
                                                <span class="text-gray-600 text-sm">Searching...</span>
                                            </div>
                                        </div>

                                        <!-- Error State -->
                                        <div v-if="error" class="p-4 m-4 bg-red-50 border border-red-200 rounded-lg">
                                            <p class="text-red-700 text-sm">{{ error }}</p>
                                        </div>

                                        <!-- Results with scroll -->
                                        <div v-if="hasResults && !isLoading" class="max-h-[700px] overflow-y-auto custom-scrollbar">
                                            <div class="p-4">
                                                <p class="text-xs text-gray-500 text-center mb-4">
                                                    {{ totalResults }} location{{ totalResults !== 1 ? 's' : '' }} found
                                                </p>

                                                <div class="space-y-4">
                                                    <!-- Provinces -->
                                                    <div v-if="searchResults.provinces.length > 0">
                                                        <h5 class="text-xs font-semibold text-gorilla-primary uppercase tracking-wide mb-3 px-1">Provinces</h5>
                                                        <div class="space-y-2">
                                                            <Card v-for="result in searchResults.provinces" :key="result.name"
                                                                class="border border-gray-200 hover:border-gorilla-primary hover:shadow-md transition-all cursor-pointer">
                                                                <CardContent class="p-4">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex items-center space-x-3">
                                                                            <div class="w-10 h-10 bg-gorilla-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                                                                                <Building class="h-5 w-5 text-gorilla-primary" />
                                                                            </div>
                                                                            <div class="min-w-0 flex-1">
                                                                                <h4 class="text-sm font-semibold text-gorilla-primary-three truncate">{{ result.name }}</h4>
                                                                                <p v-if="result.name_local" class="text-xs text-gray-500 truncate">{{ result.name_local }}</p>
                                                                            </div>
                                                                        </div>
                                                                        <span class="text-xs text-gray-400 ml-2 flex-shrink-0">
                                                                            {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                        </span>
                                                                    </div>
                                                                </CardContent>
                                                            </Card>
                                                        </div>
                                                    </div>

                                                    <!-- Districts -->
                                                    <div v-if="searchResults.districts.length > 0">
                                                        <h5 class="text-xs font-semibold text-gorilla-primary-two uppercase tracking-wide mb-3 px-1">Districts</h5>
                                                        <div class="space-y-2">
                                                            <Card v-for="result in searchResults.districts" :key="result.name"
                                                                class="border border-gray-200 hover:border-gorilla-primary-two hover:shadow-md transition-all cursor-pointer">
                                                                <CardContent class="p-4">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex items-center space-x-3">
                                                                            <div class="w-10 h-10 bg-gorilla-primary-two/10 rounded-full flex items-center justify-center flex-shrink-0">
                                                                                <Building class="h-5 w-5 text-gorilla-primary-two" />
                                                                            </div>
                                                                            <div class="min-w-0 flex-1">
                                                                                <h4 class="text-sm font-semibold text-gorilla-primary-three truncate">{{ result.name }}</h4>
                                                                                <p v-if="result.address" class="text-xs text-gray-500 truncate">{{ result.address }}</p>
                                                                            </div>
                                                                        </div>
                                                                        <span class="text-xs text-gray-400 ml-2 flex-shrink-0">
                                                                            {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                        </span>
                                                                    </div>
                                                                </CardContent>
                                                            </Card>
                                                        </div>
                                                    </div>

                                                    <!-- Sectors -->
                                                    <div v-if="searchResults.sectors.length > 0">
                                                        <h5 class="text-xs font-semibold text-gorilla-primary-three uppercase tracking-wide mb-3 px-1">Sectors</h5>
                                                        <div class="space-y-2">
                                                            <Card v-for="result in searchResults.sectors" :key="result.name + result.address"
                                                                class="border border-gray-200 hover:border-gorilla-primary-three hover:shadow-md transition-all cursor-pointer">
                                                                <CardContent class="p-4">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex items-center space-x-3">
                                                                            <div class="w-10 h-10 bg-gorilla-primary-three/10 rounded-full flex items-center justify-center flex-shrink-0">
                                                                                <Home class="h-5 w-5 text-gorilla-primary-three" />
                                                                            </div>
                                                                            <div class="min-w-0 flex-1">
                                                                                <h4 class="text-sm font-semibold text-gorilla-primary-three truncate">{{ result.name }}</h4>
                                                                                <p v-if="result.address" class="text-xs text-gray-500 truncate">{{ result.address }}</p>
                                                                            </div>
                                                                        </div>
                                                                        <span class="text-xs text-gray-400 ml-2 flex-shrink-0">
                                                                            {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                        </span>
                                                                    </div>
                                                                </CardContent>
                                                            </Card>
                                                        </div>
                                                    </div>

                                                    <!-- Cells -->
                                                    <div v-if="searchResults.cells.length > 0">
                                                        <h5 class="text-xs font-semibold text-blue-600 uppercase tracking-wide mb-3 px-1">Cells</h5>
                                                        <div class="space-y-2">
                                                            <Card v-for="result in searchResults.cells" :key="result.name + result.address"
                                                                class="border border-gray-200 hover:border-blue-600 hover:shadow-md transition-all cursor-pointer">
                                                                <CardContent class="p-4">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex items-center space-x-3">
                                                                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                                                                <Home class="h-5 w-5 text-blue-600" />
                                                                            </div>
                                                                            <div class="min-w-0 flex-1">
                                                                                <h4 class="text-sm font-semibold text-gorilla-primary-three truncate">{{ result.name }}</h4>
                                                                                <p v-if="result.address" class="text-xs text-gray-500 truncate">{{ result.address }}</p>
                                                                            </div>
                                                                        </div>
                                                                        <span class="text-xs text-gray-400 ml-2 flex-shrink-0">
                                                                            {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                        </span>
                                                                    </div>
                                                                </CardContent>
                                                            </Card>
                                                        </div>
                                                    </div>

                                                    <!-- Villages -->
                                                    <div v-if="searchResults.villages.length > 0">
                                                        <h5 class="text-xs font-semibold text-green-600 uppercase tracking-wide mb-3 px-1">Villages</h5>
                                                        <div class="space-y-2">
                                                            <Card v-for="result in searchResults.villages" :key="result.name + result.address"
                                                                class="border border-gray-200 hover:border-green-600 hover:shadow-md transition-all cursor-pointer">
                                                                <CardContent class="p-4">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex items-center space-x-3">
                                                                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                                                                <Home class="h-5 w-5 text-green-600" />
                                                                            </div>
                                                                            <div class="min-w-0 flex-1">
                                                                                <h4 class="text-sm font-semibold text-gorilla-primary-three truncate">{{ result.name }}</h4>
                                                                                <p v-if="result.address" class="text-xs text-gray-500 truncate">{{ result.address }}</p>
                                                                            </div>
                                                                        </div>
                                                                        <span class="text-xs text-gray-400 ml-2 flex-shrink-0">
                                                                            {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                        </span>
                                                                    </div>
                                                                </CardContent>
                                                            </Card>
                                                        </div>
                                                    </div>

                                                    <!-- Others -->
                                                    <div v-if="searchResults.others.length > 0">
                                                        <h5 class="text-xs font-semibold text-purple-600 uppercase tracking-wide mb-3 px-1">Places & Roads</h5>
                                                        <div class="space-y-2">
                                                            <Card v-for="result in searchResults.others" :key="result.name + result.address"
                                                                class="border border-gray-200 hover:border-purple-600 hover:shadow-md transition-all cursor-pointer">
                                                                <CardContent class="p-4">
                                                                    <div class="flex items-center justify-between">
                                                                        <div class="flex items-center space-x-3">
                                                                            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                                                                                <MapPin class="h-5 w-5 text-purple-600" />
                                                                            </div>
                                                                            <div class="min-w-0 flex-1">
                                                                                <h4 class="text-sm font-semibold text-gorilla-primary-three truncate">{{ result.name }}</h4>
                                                                                <p v-if="result.moreDetails || result.address" class="text-xs text-gray-500 truncate">
                                                                                    {{ result.address }}
                                                                                    <span v-if="result.moreDetails"> • {{ result.moreDetails }}</span>
                                                                                </p>
                                                                                <p v-if="result.type" class="text-xs text-purple-600 mt-0.5">{{ result.type }}</p>
                                                                            </div>
                                                                        </div>
                                                                        <span class="text-xs text-gray-400 ml-2 flex-shrink-0">
                                                                            {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                        </span>
                                                                    </div>
                                                                </CardContent>
                                                            </Card>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                           
                        </div>
                    </div>
                </div>
                </div>
            </section>

            <!-- Footer with Creative Lines -->
            <footer class="bg-white border-t border-gray-200 mt-20">
                <div class="max-w-7xl mx-auto px-6 lg:px-12 py-16">
                    <!-- Main Footer Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
                        <!-- Company Info -->
                        <div class="space-y-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-10 h-10 bg-gorilla-primary rounded-xl flex items-center justify-center">
                                    <MapPin class="w-6 h-6 text-white" />
                                </div>
                                <span class="text-xl font-bold text-gorilla-primary-three">Rwanda Geo</span>
                            </div>
                            <p class="text-sm text-gray-600 leading-relaxed">
                                Professional geocoding platform providing comprehensive geographic data
                                for Rwanda's administrative boundaries and locations.
                            </p>
                        </div>

                        <!-- Product Links with Connecting Lines -->
                        <div class="relative">
                            <div class="absolute -left-6 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gorilla-primary/30 to-transparent hidden lg:block"></div>
                            <h4 class="font-semibold text-gorilla-primary-three mb-4 relative">
                                <span class="relative inline-block">
                                    Product
                                    <span class="absolute -bottom-1 left-0 w-full h-0.5 bg-gorilla-primary"></span>
                                </span>
                            </h4>
                            <ul class="space-y-3">
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary transition-all relative inline-block">
                                        <span class="relative z-10">Pricing</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary transition-all relative inline-block">
                                        <span class="relative z-10">Use Cases</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <!-- Resources -->
                        <div class="relative">
                            <div class="absolute -left-6 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gorilla-primary-two/30 to-transparent hidden lg:block"></div>
                            <h4 class="font-semibold text-gorilla-primary-three mb-4 relative">
                                <span class="relative inline-block">
                                    Resources
                                    <span class="absolute -bottom-1 left-0 w-full h-0.5 bg-gorilla-primary-two"></span>
                                </span>
                            </h4>
                            <ul class="space-y-3">
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary-two transition-all relative inline-block">
                                        <span class="relative z-10">Documentation</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary-two group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary-two transition-all relative inline-block">
                                        <span class="relative z-10">Guides</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary-two group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary-two transition-all relative inline-block">
                                        <span class="relative z-10">Blog</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary-two group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary-two transition-all relative inline-block">
                                        <span class="relative z-10">Support</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary-two group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <!-- Company -->
                        <div class="relative">
                            <div class="absolute -left-6 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gorilla-primary-three/30 to-transparent hidden lg:block"></div>
                            <h4 class="font-semibold text-gorilla-primary-three mb-4 relative">
                                <span class="relative inline-block">
                                    Company
                                    <span class="absolute -bottom-1 left-0 w-full h-0.5 bg-gorilla-primary-three"></span>
                                </span>
                            </h4>
                            <ul class="space-y-3">
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary-three transition-all relative inline-block">
                                        <span class="relative z-10">About Us</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary-three group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary-three transition-all relative inline-block">
                                        <span class="relative z-10">Contact</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary-three group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary-three transition-all relative inline-block">
                                        <span class="relative z-10">Privacy Policy</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary-three group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li class="group">
                                    <a href="#" class="text-sm text-gray-600 hover:text-gorilla-primary-three transition-all relative inline-block">
                                        <span class="relative z-10">Terms of Service</span>
                                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gorilla-primary-three group-hover:w-full transition-all duration-300"></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Bottom Footer with Decorative Line -->
                    <div class="pt-8 border-t border-gray-200 relative">
                        <!-- Decorative animated line -->
                        <div class="absolute top-0 left-0 right-0 h-px overflow-hidden">
                            <div class="h-full w-32 bg-gradient-to-r from-transparent via-gorilla-primary to-transparent animate-shimmer"></div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-center">
                            <!-- Copyright -->
                            <div class="md:col-span-2 flex items-center">
                                <p class="text-sm text-gray-600">
                                    © 2025 Rwanda Geo. All rights reserved.
                                </p>
                            </div>

                            <!-- Social/Stats -->
                            <div class="flex items-center justify-start md:justify-end space-x-6">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-gorilla-primary">2,847</div>
                                    <div class="text-xs text-gray-500">Locations</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-gorilla-primary-two">45.2K</div>
                                    <div class="text-xs text-gray-500">Data Points</div>
                                </div>
                            </div>

                            <!-- Location -->
                            <div class="flex items-center justify-start md:justify-end">
                                <div class="flex items-center space-x-2 text-sm text-gray-600">
                                    <MapPin class="h-4 w-4 text-gorilla-primary" />
                                    <span>Kigali, Rwanda</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Custom scrollbar for results */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Hide scrollbar for horizontal scroll */
.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

/* Shimmer animation for footer */
@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(400%);
    }
}

.animate-shimmer {
    animation: shimmer 3s infinite;
}

/* Smooth transitions */
* {
    transition-property: color, background-color, border-color, transform, opacity;
    transition-duration: 200ms;
    transition-timing-function: ease-in-out;
}
</style>